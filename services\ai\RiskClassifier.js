const tf = require('@tensorflow/tfjs');
const ss = require('simple-statistics');

/**
 * Risk Classifier for Dropout Prediction
 * Uses machine learning to classify students into risk categories
 */
class RiskClassifier {
    constructor() {
        this.model = null;
        this.featureScaler = null;
        this.riskThresholds = {
            high: 0.7,
            medium: 0.4
        };
    }

    /**
     * Train the risk classification model
     */
    async trainModel(riskData) {
        try {
            console.log('Training risk classification model...');
            
            if (riskData.length === 0) {
                console.warn('No risk data available for training');
                return null;
            }

            // Prepare training data
            const { features, labels, scaler } = this.prepareTrainingData(riskData);
            
            if (features.length === 0) {
                console.warn('Insufficient data for risk classification training');
                return null;
            }

            this.featureScaler = scaler;
            
            // Create model
            this.model = this.createRiskModel(features[0].length);
            
            // Convert to tensors
            const xs = tf.tensor2d(features);
            const ys = tf.tensor2d(labels);
            
            // Train model
            await this.model.fit(xs, ys, {
                epochs: 100,
                batchSize: 16,
                validationSplit: 0.2,
                callbacks: {
                    onEpochEnd: (epoch, logs) => {
                        if (epoch % 20 === 0) {
                            console.log(`Epoch ${epoch}: loss = ${logs.loss.toFixed(4)}, accuracy = ${logs.acc?.toFixed(4) || 'N/A'}`);
                        }
                    }
                }
            });
            
            // Clean up tensors
            xs.dispose();
            ys.dispose();
            
            console.log('Risk classification model trained successfully');
            return this.model;
        } catch (error) {
            console.error('Error training risk classification model:', error);
            throw error;
        }
    }

    /**
     * Create the risk classification neural network model
     */
    createRiskModel(inputDim) {
        const model = tf.sequential({
            layers: [
                tf.layers.dense({
                    units: 64,
                    activation: 'relu',
                    inputShape: [inputDim]
                }),
                tf.layers.dropout({ rate: 0.3 }),
                tf.layers.dense({
                    units: 32,
                    activation: 'relu'
                }),
                tf.layers.dropout({ rate: 0.3 }),
                tf.layers.dense({
                    units: 16,
                    activation: 'relu'
                }),
                tf.layers.dense({
                    units: 3,
                    activation: 'softmax' // 3 classes: low, medium, high risk
                })
            ]
        });

        model.compile({
            optimizer: tf.train.adam(0.001),
            loss: 'categoricalCrossentropy',
            metrics: ['accuracy']
        });

        return model;
    }

    /**
     * Prepare training data for risk classification
     */
    prepareTrainingData(riskData) {
        const features = [];
        const labels = [];
        
        // Extract features and labels
        riskData.forEach(data => {
            if (data.features && data.label) {
                features.push(data.features);
                labels.push(this.labelToOneHot(data.label));
            }
        });
        
        if (features.length === 0) {
            return { features: [], labels: [], scaler: null };
        }

        // Normalize features
        const scaler = this.createFeatureScaler(features);
        const normalizedFeatures = this.normalizeFeatures(features, scaler);
        
        return {
            features: normalizedFeatures,
            labels,
            scaler
        };
    }

    /**
     * Create feature scaler for normalization
     */
    createFeatureScaler(features) {
        const numFeatures = features[0].length;
        const scaler = {
            means: [],
            stds: []
        };
        
        for (let i = 0; i < numFeatures; i++) {
            const featureValues = features.map(f => f[i]);
            scaler.means[i] = ss.mean(featureValues);
            scaler.stds[i] = ss.standardDeviation(featureValues) || 1; // Avoid division by zero
        }
        
        return scaler;
    }

    /**
     * Normalize features using the scaler
     */
    normalizeFeatures(features, scaler) {
        return features.map(feature => 
            feature.map((value, index) => 
                (value - scaler.means[index]) / scaler.stds[index]
            )
        );
    }

    /**
     * Convert risk label to one-hot encoding
     */
    labelToOneHot(label) {
        switch (label) {
            case 'low': return [1, 0, 0];
            case 'medium': return [0, 1, 0];
            case 'high': return [0, 0, 1];
            default: return [1, 0, 0]; // Default to low risk
        }
    }

    /**
     * Convert one-hot encoding to risk label
     */
    oneHotToLabel(oneHot) {
        const maxIndex = oneHot.indexOf(Math.max(...oneHot));
        switch (maxIndex) {
            case 0: return 'low';
            case 1: return 'medium';
            case 2: return 'high';
            default: return 'low';
        }
    }

    /**
     * Assess risk for a student based on their data
     */
    async assessRisk(preprocessedData) {
        try {
            const { features } = preprocessedData;
            
            // Calculate rule-based risk assessment
            const ruleBasedRisk = this.calculateRuleBasedRisk(features);
            
            // If model is available, use ML prediction
            let mlRisk = null;
            if (this.model && this.featureScaler) {
                mlRisk = await this.predictRiskWithModel(features);
            }
            
            // Combine assessments
            const finalRisk = this.combineRiskAssessments(ruleBasedRisk, mlRisk);
            
            return {
                riskLevel: finalRisk.level,
                confidence: finalRisk.confidence,
                factors: finalRisk.factors,
                recommendations: this.generateRecommendations(finalRisk),
                ruleBasedAssessment: ruleBasedRisk,
                mlAssessment: mlRisk
            };
        } catch (error) {
            console.error('Error assessing risk:', error);
            throw error;
        }
    }

    /**
     * Calculate rule-based risk assessment
     */
    calculateRuleBasedRisk(features) {
        const riskFactors = [];
        let riskScore = 0;
        
        // Attendance rate factor
        if (features.attendanceRate < 0.6) {
            riskFactors.push('Very low attendance rate');
            riskScore += 0.4;
        } else if (features.attendanceRate < 0.8) {
            riskFactors.push('Low attendance rate');
            riskScore += 0.2;
        }
        
        // Absent rate factor
        if (features.absentRate > 0.4) {
            riskFactors.push('High absence rate');
            riskScore += 0.3;
        } else if (features.absentRate > 0.2) {
            riskFactors.push('Moderate absence rate');
            riskScore += 0.15;
        }
        
        // Recent trend factor
        if (features.recentTrend < -0.2) {
            riskFactors.push('Declining attendance trend');
            riskScore += 0.25;
        } else if (features.recentTrend < -0.1) {
            riskFactors.push('Slightly declining trend');
            riskScore += 0.1;
        }
        
        // Longest absent streak factor
        if (features.longestAbsentStreak > 7) {
            riskFactors.push('Extended absence periods');
            riskScore += 0.3;
        } else if (features.longestAbsentStreak > 3) {
            riskFactors.push('Moderate absence streaks');
            riskScore += 0.15;
        }
        
        // Consistency factor
        if (features.consistency < 0.5) {
            riskFactors.push('Inconsistent attendance pattern');
            riskScore += 0.2;
        }
        
        // Weekly variability factor
        if (features.weeklyVariability > 0.4) {
            riskFactors.push('High weekly attendance variability');
            riskScore += 0.15;
        }
        
        // Determine risk level
        let level = 'low';
        if (riskScore >= 0.7) level = 'high';
        else if (riskScore >= 0.4) level = 'medium';
        
        return {
            level,
            score: riskScore,
            factors: riskFactors,
            confidence: Math.min(0.9, 0.6 + (riskFactors.length * 0.1))
        };
    }

    /**
     * Predict risk using the trained model
     */
    async predictRiskWithModel(features) {
        if (!this.model || !this.featureScaler) {
            return null;
        }

        try {
            // Convert features object to array
            const featureArray = [
                features.totalSessions || 0,
                features.attendanceRate || 0,
                features.lateRate || 0,
                features.absentRate || 0,
                features.recentTrend || 0,
                features.weeklyVariability || 0,
                features.monthlyVariability || 0,
                features.consistency || 0,
                features.longestAbsentStreak || 0,
                features.longestPresentStreak || 0,
                features.averageGapBetweenAbsences || 0
            ];
            
            // Normalize features
            const normalizedFeatures = this.normalizeFeatures([featureArray], this.featureScaler)[0];
            
            // Make prediction
            const inputTensor = tf.tensor2d([normalizedFeatures]);
            const prediction = this.model.predict(inputTensor);
            const predictionData = await prediction.data();
            
            // Clean up tensors
            inputTensor.dispose();
            prediction.dispose();
            
            // Convert to risk assessment
            const probabilities = Array.from(predictionData);
            const maxProbability = Math.max(...probabilities);
            const predictedLevel = this.oneHotToLabel(probabilities);
            
            return {
                level: predictedLevel,
                confidence: maxProbability,
                probabilities: {
                    low: probabilities[0],
                    medium: probabilities[1],
                    high: probabilities[2]
                }
            };
        } catch (error) {
            console.error('Error predicting risk with model:', error);
            return null;
        }
    }

    /**
     * Combine rule-based and ML risk assessments
     */
    combineRiskAssessments(ruleBasedRisk, mlRisk) {
        if (!mlRisk) {
            return ruleBasedRisk;
        }
        
        // Weight the assessments
        const ruleWeight = 0.4;
        const mlWeight = 0.6;
        
        // Convert levels to numeric scores
        const levelToScore = { low: 0, medium: 0.5, high: 1 };
        const scoreToLevel = (score) => {
            if (score >= 0.7) return 'high';
            if (score >= 0.4) return 'medium';
            return 'low';
        };
        
        const ruleScore = levelToScore[ruleBasedRisk.level];
        const mlScore = levelToScore[mlRisk.level];
        
        const combinedScore = (ruleScore * ruleWeight) + (mlScore * mlWeight);
        const combinedLevel = scoreToLevel(combinedScore);
        
        const combinedConfidence = (ruleBasedRisk.confidence * ruleWeight) + (mlRisk.confidence * mlWeight);
        
        return {
            level: combinedLevel,
            confidence: combinedConfidence,
            score: combinedScore,
            factors: ruleBasedRisk.factors
        };
    }

    /**
     * Generate recommendations based on risk assessment
     */
    generateRecommendations(riskAssessment) {
        const recommendations = [];
        
        switch (riskAssessment.level) {
            case 'high':
                recommendations.push('Immediate intervention required');
                recommendations.push('Schedule parent-teacher conference');
                recommendations.push('Develop individualized attendance plan');
                recommendations.push('Consider counseling support');
                recommendations.push('Monitor daily attendance closely');
                break;
                
            case 'medium':
                recommendations.push('Increased monitoring recommended');
                recommendations.push('Contact parents about attendance concerns');
                recommendations.push('Identify and address attendance barriers');
                recommendations.push('Provide additional academic support if needed');
                break;
                
            case 'low':
                recommendations.push('Continue regular monitoring');
                recommendations.push('Maintain positive reinforcement');
                recommendations.push('Recognize good attendance');
                break;
        }
        
        // Add factor-specific recommendations
        if (riskAssessment.factors) {
            riskAssessment.factors.forEach(factor => {
                if (factor.includes('declining trend')) {
                    recommendations.push('Investigate recent changes affecting attendance');
                }
                if (factor.includes('absence streaks')) {
                    recommendations.push('Implement early warning system for consecutive absences');
                }
                if (factor.includes('inconsistent')) {
                    recommendations.push('Work with family to establish consistent routines');
                }
            });
        }
        
        return [...new Set(recommendations)]; // Remove duplicates
    }

    /**
     * Calculate risk trend over time
     */
    calculateRiskTrend(historicalRiskData) {
        if (historicalRiskData.length < 3) {
            return { trend: 'insufficient_data', slope: 0 };
        }
        
        const riskScores = historicalRiskData.map(data => {
            switch (data.level) {
                case 'high': return 1;
                case 'medium': return 0.5;
                case 'low': return 0;
                default: return 0;
            }
        });
        
        const indices = Array.from({ length: riskScores.length }, (_, i) => i);
        const regression = ss.linearRegression(indices.map((x, i) => [x, riskScores[i]]));
        
        let trend = 'stable';
        if (regression.m > 0.1) trend = 'increasing';
        else if (regression.m < -0.1) trend = 'decreasing';
        
        return {
            trend,
            slope: regression.m,
            rSquared: ss.rSquared(indices.map((x, i) => [x, riskScores[i]]), ss.linearRegressionLine(regression))
        };
    }

    /**
     * Get risk statistics for a group of students
     */
    calculateGroupRiskStatistics(riskAssessments) {
        const total = riskAssessments.length;
        if (total === 0) {
            return {
                total: 0,
                distribution: { high: 0, medium: 0, low: 0 },
                percentages: { high: 0, medium: 0, low: 0 }
            };
        }
        
        const distribution = {
            high: riskAssessments.filter(r => r.riskLevel === 'high').length,
            medium: riskAssessments.filter(r => r.riskLevel === 'medium').length,
            low: riskAssessments.filter(r => r.riskLevel === 'low').length
        };
        
        const percentages = {
            high: (distribution.high / total) * 100,
            medium: (distribution.medium / total) * 100,
            low: (distribution.low / total) * 100
        };
        
        return {
            total,
            distribution,
            percentages
        };
    }
}

module.exports = RiskClassifier;
