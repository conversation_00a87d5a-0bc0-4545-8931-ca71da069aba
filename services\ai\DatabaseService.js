const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

/**
 * Database Service for AI Analytics
 * Handles all database operations for AI analytics features
 */
class DatabaseService {
    constructor(dbPath = null) {
        this.dbPath = dbPath || path.join(__dirname, '../../database/attendance.db');
        this.db = null;
    }

    /**
     * Initialize database connection
     */
    initialize() {
        try {
            this.db = new Database(this.dbPath);
            this.db.pragma('journal_mode = WAL');
            this.db.pragma('foreign_keys = ON');
            console.log('AI Analytics Database Service initialized');
            return true;
        } catch (error) {
            console.error('Error initializing database service:', error);
            throw error;
        }
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) {
            this.db.close();
            this.db = null;
        }
    }

    /**
     * Run database migration for AI analytics tables
     */
    async runMigration() {
        try {
            const migrationPath = path.join(__dirname, '../../database/migrations/add_ai_analytics_tables.sql');
            
            if (!fs.existsSync(migrationPath)) {
                console.warn('Migration file not found:', migrationPath);
                return false;
            }

            const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
            this.db.exec(migrationSQL);
            
            console.log('AI Analytics migration completed successfully');
            return true;
        } catch (error) {
            console.error('Error running migration:', error);
            throw error;
        }
    }

    // ============================================================================
    // AI MODELS OPERATIONS
    // ============================================================================

    /**
     * Save AI model metadata
     */
    saveModel(modelData) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO ai_models 
            (model_name, model_type, model_version, model_path, training_data_hash, 
             accuracy_score, training_date, hyperparameters, feature_names, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        return stmt.run(
            modelData.name,
            modelData.type,
            modelData.version,
            modelData.path,
            modelData.dataHash,
            modelData.accuracy,
            modelData.trainingDate,
            JSON.stringify(modelData.hyperparameters || {}),
            JSON.stringify(modelData.featureNames || []),
            modelData.isActive ? 1 : 0
        );
    }

    /**
     * Get AI model by name and type
     */
    getModel(modelName, modelType) {
        const stmt = this.db.prepare(`
            SELECT * FROM ai_models 
            WHERE model_name = ? AND model_type = ? AND is_active = 1
        `);
        return stmt.get(modelName, modelType);
    }

    /**
     * Update model last used timestamp
     */
    updateModelLastUsed(modelId) {
        const stmt = this.db.prepare(`
            UPDATE ai_models SET last_used = CURRENT_TIMESTAMP WHERE model_id = ?
        `);
        return stmt.run(modelId);
    }

    // ============================================================================
    // RISK ASSESSMENTS OPERATIONS
    // ============================================================================

    /**
     * Save student risk assessment
     */
    saveRiskAssessment(assessmentData) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO student_risk_assessments 
            (student_id, assessment_date, risk_level, risk_score, confidence_score, 
             model_id, risk_factors, recommendations, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        return stmt.run(
            assessmentData.studentId,
            assessmentData.assessmentDate,
            assessmentData.riskLevel,
            assessmentData.riskScore,
            assessmentData.confidence,
            assessmentData.modelId,
            JSON.stringify(assessmentData.factors || []),
            JSON.stringify(assessmentData.recommendations || []),
            assessmentData.createdBy
        );
    }

    /**
     * Get latest risk assessment for a student
     */
    getLatestRiskAssessment(studentId) {
        const stmt = this.db.prepare(`
            SELECT * FROM student_risk_assessments 
            WHERE student_id = ? 
            ORDER BY assessment_date DESC 
            LIMIT 1
        `);
        return stmt.get(studentId);
    }

    /**
     * Get risk assessments for multiple students
     */
    getRiskAssessments(studentIds, limit = 100) {
        const placeholders = studentIds.map(() => '?').join(',');
        const stmt = this.db.prepare(`
            SELECT sra.*, s.first_name, s.last_name, s.grade_level, s.section
            FROM student_risk_assessments sra
            JOIN students s ON sra.student_id = s.student_id
            WHERE sra.student_id IN (${placeholders})
            ORDER BY sra.assessment_date DESC
            LIMIT ?
        `);
        return stmt.all(...studentIds, limit);
    }

    // ============================================================================
    // ATTENDANCE PATTERNS OPERATIONS
    // ============================================================================

    /**
     * Save attendance pattern analysis
     */
    saveAttendancePattern(patternData) {
        const stmt = this.db.prepare(`
            INSERT INTO attendance_patterns 
            (student_id, analysis_date, pattern_type, pattern_data, strength, 
             significance, model_id, timeframe_start, timeframe_end)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        return stmt.run(
            patternData.studentId,
            patternData.analysisDate,
            patternData.patternType,
            JSON.stringify(patternData.patternData),
            patternData.strength,
            patternData.significance,
            patternData.modelId,
            patternData.timeframeStart,
            patternData.timeframeEnd
        );
    }

    /**
     * Get attendance patterns for a student
     */
    getAttendancePatterns(studentId, patternType = null, limit = 50) {
        let query = `
            SELECT * FROM attendance_patterns 
            WHERE student_id = ?
        `;
        const params = [studentId];

        if (patternType) {
            query += ` AND pattern_type = ?`;
            params.push(patternType);
        }

        query += ` ORDER BY analysis_date DESC LIMIT ?`;
        params.push(limit);

        const stmt = this.db.prepare(query);
        return stmt.all(...params);
    }

    // ============================================================================
    // STUDENT CLUSTERS OPERATIONS
    // ============================================================================

    /**
     * Save student cluster assignment
     */
    saveClusterAssignment(clusterData) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO student_clusters 
            (student_id, cluster_id, cluster_label, assignment_date, confidence_score, 
             distance_to_centroid, model_id, cluster_characteristics)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);

        return stmt.run(
            clusterData.studentId,
            clusterData.clusterId,
            clusterData.clusterLabel,
            clusterData.assignmentDate,
            clusterData.confidence,
            clusterData.distance,
            clusterData.modelId,
            JSON.stringify(clusterData.characteristics || {})
        );
    }

    /**
     * Get cluster distribution
     */
    getClusterDistribution(assignmentDate = null) {
        let query = `
            SELECT cluster_label, COUNT(*) as count
            FROM student_clusters
        `;
        const params = [];

        if (assignmentDate) {
            query += ` WHERE assignment_date = ?`;
            params.push(assignmentDate);
        } else {
            // Get latest assignments for each student
            query = `
                SELECT cluster_label, COUNT(*) as count
                FROM (
                    SELECT student_id, cluster_label,
                           ROW_NUMBER() OVER (PARTITION BY student_id ORDER BY assignment_date DESC) as rn
                    FROM student_clusters
                ) WHERE rn = 1
            `;
        }

        query += ` GROUP BY cluster_label ORDER BY count DESC`;

        const stmt = this.db.prepare(query);
        return stmt.all(...params);
    }

    // ============================================================================
    // PREDICTIONS OPERATIONS
    // ============================================================================

    /**
     * Save attendance prediction
     */
    savePrediction(predictionData) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO attendance_predictions 
            (student_id, prediction_date, target_date, predicted_status, probability, 
             confidence_score, model_id, features_used)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);

        return stmt.run(
            predictionData.studentId,
            predictionData.predictionDate,
            predictionData.targetDate,
            predictionData.predictedStatus,
            predictionData.probability,
            predictionData.confidence,
            predictionData.modelId,
            JSON.stringify(predictionData.features || {})
        );
    }

    /**
     * Update prediction with actual result
     */
    updatePredictionAccuracy(predictionId, actualStatus) {
        const stmt = this.db.prepare(`
            UPDATE attendance_predictions 
            SET actual_status = ?, 
                prediction_accuracy = CASE 
                    WHEN predicted_status = ? THEN 1.0 
                    ELSE 0.0 
                END,
                updated_at = CURRENT_TIMESTAMP
            WHERE prediction_id = ?
        `);

        return stmt.run(actualStatus, actualStatus, predictionId);
    }

    // ============================================================================
    // PERFORMANCE CORRELATIONS OPERATIONS
    // ============================================================================

    /**
     * Save performance correlation analysis
     */
    saveCorrelationAnalysis(correlationData) {
        const stmt = this.db.prepare(`
            INSERT INTO performance_correlations 
            (analysis_date, correlation_type, entity_id, entity_type, correlation_coefficient, 
             correlation_strength, sample_size, p_value, r_squared, analysis_details, model_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        return stmt.run(
            correlationData.analysisDate,
            correlationData.correlationType,
            correlationData.entityId,
            correlationData.entityType,
            correlationData.coefficient,
            correlationData.strength,
            correlationData.sampleSize,
            correlationData.pValue,
            correlationData.rSquared,
            JSON.stringify(correlationData.details || {}),
            correlationData.modelId
        );
    }

    // ============================================================================
    // EARLY WARNING ALERTS OPERATIONS
    // ============================================================================

    /**
     * Create early warning alert
     */
    createAlert(alertData) {
        const stmt = this.db.prepare(`
            INSERT INTO early_warning_alerts 
            (student_id, alert_type, severity, alert_message, alert_data, triggered_by)
            VALUES (?, ?, ?, ?, ?, ?)
        `);

        return stmt.run(
            alertData.studentId,
            alertData.alertType,
            alertData.severity,
            alertData.message,
            JSON.stringify(alertData.data || {}),
            alertData.triggeredBy
        );
    }

    /**
     * Get active alerts
     */
    getActiveAlerts(studentId = null, severity = null, limit = 100) {
        let query = `
            SELECT ewa.*, s.first_name, s.last_name, s.grade_level, s.section
            FROM early_warning_alerts ewa
            JOIN students s ON ewa.student_id = s.student_id
            WHERE ewa.status = 'active'
        `;
        const params = [];

        if (studentId) {
            query += ` AND ewa.student_id = ?`;
            params.push(studentId);
        }

        if (severity) {
            query += ` AND ewa.severity = ?`;
            params.push(severity);
        }

        query += ` ORDER BY ewa.created_at DESC LIMIT ?`;
        params.push(limit);

        const stmt = this.db.prepare(query);
        return stmt.all(...params);
    }

    // ============================================================================
    // UTILITY METHODS
    // ============================================================================

    /**
     * Get attendance data for AI analysis
     */
    getAttendanceDataForAnalysis(studentIds = null, dateFrom = null, dateTo = null) {
        let query = `
            SELECT a.*, s.first_name, s.last_name, s.grade_level, s.section,
                   cs.date_time, cs.subject_id, sub.subject_name
            FROM attendance a
            JOIN students s ON a.student_id = s.student_id
            JOIN class_sessions cs ON a.session_id = cs.session_id
            LEFT JOIN subjects sub ON cs.subject_id = sub.subject_id
            WHERE 1=1
        `;
        const params = [];

        if (studentIds && studentIds.length > 0) {
            const placeholders = studentIds.map(() => '?').join(',');
            query += ` AND a.student_id IN (${placeholders})`;
            params.push(...studentIds);
        }

        if (dateFrom) {
            query += ` AND DATE(cs.date_time) >= ?`;
            params.push(dateFrom);
        }

        if (dateTo) {
            query += ` AND DATE(cs.date_time) <= ?`;
            params.push(dateTo);
        }

        query += ` ORDER BY a.student_id, cs.date_time`;

        const stmt = this.db.prepare(query);
        return stmt.all(...params);
    }

    /**
     * Get student performance data (placeholder - adjust based on actual schema)
     */
    getPerformanceDataForAnalysis(studentIds = null) {
        // Note: This is a placeholder - adjust based on actual performance/grades schema
        let query = `
            SELECT student_id, 'placeholder' as subject_name, 0 as grade, 
                   CURRENT_DATE as assessment_date
            FROM students
            WHERE 1=1
        `;
        const params = [];

        if (studentIds && studentIds.length > 0) {
            const placeholders = studentIds.map(() => '?').join(',');
            query += ` AND student_id IN (${placeholders})`;
            params.push(...studentIds);
        }

        const stmt = this.db.prepare(query);
        return stmt.all(...params);
    }
}

module.exports = DatabaseService;
