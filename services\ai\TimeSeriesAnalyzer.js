const tf = require('@tensorflow/tfjs');
const ss = require('simple-statistics');
const { format, addDays, subDays, differenceInDays, parseISO } = require('date-fns');

/**
 * Time Series Analyzer for Attendance Patterns
 * Uses TensorFlow.js for time series analysis and prediction
 */
class TimeSeriesAnalyzer {
    constructor() {
        this.model = null;
        this.sequenceLength = 14; // 2 weeks of data
        this.predictionHorizon = 7; // Predict next week
        this.anomalyThreshold = 2.0; // Standard deviations for anomaly detection
        this.minDataPoints = 10; // Minimum data points for analysis
        this.modelMetrics = {
            lastTrainingLoss: null,
            lastValidationLoss: null,
            trainingDate: null
        };
    }

    /**
     * Train the time series model
     */
    async trainModel(timeSeriesData) {
        try {
            console.log('Training time series model...');
            
            if (timeSeriesData.length === 0) {
                console.warn('No time series data available for training');
                return null;
            }

            // Prepare training data
            const { sequences, targets } = this.prepareSequences(timeSeriesData);
            
            if (sequences.length === 0) {
                console.warn('Insufficient data for time series training');
                return null;
            }

            // Create model
            this.model = this.createTimeSeriesModel();
            
            // Convert to tensors
            const xs = tf.tensor3d(sequences);
            const ys = tf.tensor2d(targets);
            
            // Train model with enhanced callbacks
            const history = await this.model.fit(xs, ys, {
                epochs: 100,
                batchSize: Math.min(32, Math.floor(sequences.length / 4)),
                validationSplit: 0.2,
                shuffle: true,
                callbacks: {
                    onEpochEnd: (epoch, logs) => {
                        if (epoch % 20 === 0) {
                            console.log(`Epoch ${epoch}: loss = ${logs.loss.toFixed(4)}, val_loss = ${logs.val_loss?.toFixed(4) || 'N/A'}`);
                        }

                        // Store final metrics
                        if (epoch === 99) {
                            this.modelMetrics.lastTrainingLoss = logs.loss;
                            this.modelMetrics.lastValidationLoss = logs.val_loss;
                            this.modelMetrics.trainingDate = new Date().toISOString();
                        }
                    }
                }
            });
            
            // Clean up tensors
            xs.dispose();
            ys.dispose();
            
            console.log('Time series model trained successfully');
            return this.model;
        } catch (error) {
            console.error('Error training time series model:', error);
            throw error;
        }
    }

    /**
     * Create the time series neural network model with enhanced architecture
     */
    createTimeSeriesModel() {
        const model = tf.sequential({
            layers: [
                // First LSTM layer with more units
                tf.layers.lstm({
                    units: 64,
                    returnSequences: true,
                    inputShape: [this.sequenceLength, 1],
                    recurrentDropout: 0.1
                }),
                tf.layers.dropout({ rate: 0.3 }),

                // Second LSTM layer
                tf.layers.lstm({
                    units: 32,
                    returnSequences: false,
                    recurrentDropout: 0.1
                }),
                tf.layers.dropout({ rate: 0.3 }),

                // Dense layers with batch normalization
                tf.layers.dense({
                    units: 50,
                    activation: 'relu'
                }),
                tf.layers.dropout({ rate: 0.2 }),

                tf.layers.dense({
                    units: 25,
                    activation: 'relu'
                }),
                tf.layers.dropout({ rate: 0.1 }),

                // Output layer
                tf.layers.dense({
                    units: this.predictionHorizon,
                    activation: 'sigmoid' // Ensures output between 0 and 1
                })
            ]
        });

        // Enhanced optimizer with learning rate scheduling
        const optimizer = tf.train.adam(0.001);

        model.compile({
            optimizer: optimizer,
            loss: 'meanSquaredError',
            metrics: ['mae', 'mse']
        });

        console.log('Time series model architecture:');
        model.summary();

        return model;
    }

    /**
     * Prepare sequences for training
     */
    prepareSequences(timeSeriesData) {
        const sequences = [];
        const targets = [];
        
        timeSeriesData.forEach(studentData => {
            const timeSeries = studentData.timeSeries;
            
            if (timeSeries.length < this.sequenceLength + this.predictionHorizon) {
                return; // Skip if not enough data
            }

            // Create sequences
            for (let i = 0; i <= timeSeries.length - this.sequenceLength - this.predictionHorizon; i++) {
                const sequence = timeSeries.slice(i, i + this.sequenceLength).map(point => point.value);
                const target = timeSeries.slice(i + this.sequenceLength, i + this.sequenceLength + this.predictionHorizon).map(point => point.value);
                
                sequences.push(sequence);
                targets.push(target);
            }
        });
        
        return { sequences, targets };
    }

    /**
     * Analyze attendance patterns for a student
     */
    async analyzePatterns(preprocessedData) {
        try {
            const { timeSeries, features } = preprocessedData;
            
            // Basic pattern analysis
            const patterns = {
                trend: this.analyzeTrend(timeSeries),
                seasonality: this.analyzeSeasonality(timeSeries),
                volatility: this.analyzeVolatility(timeSeries),
                weeklyPattern: this.analyzeWeeklyPattern(timeSeries),
                predictions: null
            };
            
            // Generate predictions if model is available
            if (this.model && timeSeries.length >= this.sequenceLength) {
                patterns.predictions = await this.generatePredictions(timeSeries);
            }
            
            return patterns;
        } catch (error) {
            console.error('Error analyzing patterns:', error);
            throw error;
        }
    }

    /**
     * Analyze trend in time series
     */
    analyzeTrend(timeSeries) {
        if (timeSeries.length < 5) {
            return { direction: 'insufficient_data', strength: 0, slope: 0 };
        }

        const values = timeSeries.map(point => point.value);
        const indices = Array.from({ length: values.length }, (_, i) => i);
        
        // Calculate linear regression
        const regression = ss.linearRegression(indices.map((x, i) => [x, values[i]]));
        const slope = regression.m;
        
        // Determine trend direction and strength
        let direction = 'stable';
        if (slope > 0.01) direction = 'improving';
        else if (slope < -0.01) direction = 'declining';
        
        const strength = Math.abs(slope);
        
        return {
            direction,
            strength,
            slope,
            rSquared: ss.rSquared(indices.map((x, i) => [x, values[i]]), ss.linearRegressionLine(regression))
        };
    }

    /**
     * Analyze seasonality patterns
     */
    analyzeSeasonality(timeSeries) {
        if (timeSeries.length < 14) {
            return { hasSeasonality: false, period: null, strength: 0 };
        }

        // Check for weekly seasonality (7-day period)
        const weeklyPattern = this.detectPeriodicity(timeSeries, 7);
        
        // Check for monthly patterns (30-day period)
        const monthlyPattern = this.detectPeriodicity(timeSeries, 30);
        
        return {
            weekly: weeklyPattern,
            monthly: monthlyPattern,
            hasSeasonality: weeklyPattern.strength > 0.3 || monthlyPattern.strength > 0.3
        };
    }

    /**
     * Detect periodicity in time series
     */
    detectPeriodicity(timeSeries, period) {
        if (timeSeries.length < period * 2) {
            return { strength: 0, correlation: 0 };
        }

        const values = timeSeries.map(point => point.value);
        
        // Calculate autocorrelation at the given period
        const correlation = this.calculateAutocorrelation(values, period);
        
        return {
            strength: Math.abs(correlation),
            correlation,
            period
        };
    }

    /**
     * Calculate autocorrelation
     */
    calculateAutocorrelation(values, lag) {
        if (values.length <= lag) return 0;
        
        const n = values.length - lag;
        const x1 = values.slice(0, n);
        const x2 = values.slice(lag);
        
        return ss.sampleCorrelation(x1, x2);
    }

    /**
     * Analyze volatility in attendance
     */
    analyzeVolatility(timeSeries) {
        if (timeSeries.length < 5) {
            return { level: 'insufficient_data', value: 0 };
        }

        const values = timeSeries.map(point => point.value);
        const standardDeviation = ss.standardDeviation(values);
        const mean = ss.mean(values);
        
        // Calculate coefficient of variation
        const coefficientOfVariation = mean > 0 ? standardDeviation / mean : 0;
        
        let level = 'low';
        if (coefficientOfVariation > 0.5) level = 'high';
        else if (coefficientOfVariation > 0.3) level = 'medium';
        
        return {
            level,
            value: coefficientOfVariation,
            standardDeviation,
            mean
        };
    }

    /**
     * Analyze weekly attendance patterns
     */
    analyzeWeeklyPattern(timeSeries) {
        const dayPatterns = {
            0: [], // Sunday
            1: [], // Monday
            2: [], // Tuesday
            3: [], // Wednesday
            4: [], // Thursday
            5: [], // Friday
            6: []  // Saturday
        };
        
        timeSeries.forEach(point => {
            if (point.dayOfWeek !== undefined) {
                dayPatterns[point.dayOfWeek].push(point.value);
            }
        });
        
        const weeklyStats = {};
        Object.keys(dayPatterns).forEach(day => {
            const values = dayPatterns[day];
            if (values.length > 0) {
                weeklyStats[day] = {
                    average: ss.mean(values),
                    count: values.length,
                    dayName: this.getDayName(parseInt(day))
                };
            }
        });
        
        return weeklyStats;
    }

    /**
     * Generate predictions using the trained model
     */
    async generatePredictions(timeSeries) {
        if (!this.model || timeSeries.length < this.sequenceLength) {
            return null;
        }

        try {
            // Prepare input sequence
            const lastSequence = timeSeries.slice(-this.sequenceLength).map(point => point.value);
            const inputTensor = tf.tensor3d([lastSequence], [1, this.sequenceLength, 1]);
            
            // Generate prediction
            const prediction = this.model.predict(inputTensor);
            const predictionData = await prediction.data();
            
            // Clean up tensors
            inputTensor.dispose();
            prediction.dispose();
            
            // Create prediction points
            const lastDate = new Date(timeSeries[timeSeries.length - 1].date);
            const predictions = Array.from(predictionData).map((value, index) => ({
                date: format(addDays(lastDate, index + 1), 'yyyy-MM-dd'),
                predictedValue: Math.max(0, Math.min(1, value)), // Clamp between 0 and 1
                confidence: this.calculatePredictionConfidence(value, index)
            }));
            
            return predictions;
        } catch (error) {
            console.error('Error generating predictions:', error);
            return null;
        }
    }

    /**
     * Calculate prediction confidence
     */
    calculatePredictionConfidence(value, dayIndex) {
        // Confidence decreases with prediction horizon
        const baseConfidence = 0.8;
        const decayRate = 0.1;
        
        // Confidence also depends on how "reasonable" the prediction is
        const valueConfidence = 1 - Math.abs(value - 0.8); // Assume 0.8 is typical attendance
        
        return Math.max(0.1, baseConfidence * Math.exp(-decayRate * dayIndex) * valueConfidence);
    }

    /**
     * Detect anomalies in attendance patterns
     */
    detectAnomalies(timeSeries, threshold = 2) {
        if (timeSeries.length < 10) return [];
        
        const values = timeSeries.map(point => point.value);
        const mean = ss.mean(values);
        const stdDev = ss.standardDeviation(values);
        
        const anomalies = [];
        
        timeSeries.forEach((point, index) => {
            const zScore = Math.abs((point.value - mean) / stdDev);
            if (zScore > threshold) {
                anomalies.push({
                    date: point.date,
                    value: point.value,
                    zScore,
                    type: point.value < mean ? 'low_attendance' : 'high_attendance'
                });
            }
        });
        
        return anomalies;
    }

    /**
     * Calculate attendance streaks
     */
    calculateStreaks(timeSeries) {
        let currentStreak = 0;
        let longestStreak = 0;
        let streakType = null;
        const streaks = [];
        
        timeSeries.forEach((point, index) => {
            const isPresent = point.value >= 0.5; // Consider late as present for streaks
            
            if (index === 0) {
                currentStreak = 1;
                streakType = isPresent ? 'present' : 'absent';
            } else {
                const wasPresent = timeSeries[index - 1].value >= 0.5;
                
                if (isPresent === wasPresent) {
                    currentStreak++;
                } else {
                    // Streak ended
                    if (currentStreak > 0) {
                        streaks.push({
                            type: streakType,
                            length: currentStreak,
                            endDate: timeSeries[index - 1].date
                        });
                    }
                    
                    currentStreak = 1;
                    streakType = isPresent ? 'present' : 'absent';
                }
                
                if (streakType === 'present') {
                    longestStreak = Math.max(longestStreak, currentStreak);
                }
            }
        });
        
        // Add final streak
        if (currentStreak > 0) {
            streaks.push({
                type: streakType,
                length: currentStreak,
                endDate: timeSeries[timeSeries.length - 1].date
            });
        }
        
        return {
            current: { type: streakType, length: currentStreak },
            longest: longestStreak,
            all: streaks
        };
    }

    /**
     * Helper method to get day name
     */
    getDayName(dayIndex) {
        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        return days[dayIndex] || 'Unknown';
    }

    // ============================================================================
    // ADVANCED PATTERN ANALYSIS METHODS
    // ============================================================================

    /**
     * Comprehensive pattern analysis with multiple algorithms
     */
    async analyzeAdvancedPatterns(preprocessedData) {
        try {
            const { timeSeries, features } = preprocessedData;

            if (timeSeries.length < this.minDataPoints) {
                return {
                    error: 'Insufficient data for pattern analysis',
                    dataPoints: timeSeries.length,
                    required: this.minDataPoints
                };
            }

            const patterns = {
                // Basic patterns
                trend: this.analyzeTrend(timeSeries),
                seasonality: this.analyzeSeasonality(timeSeries),
                volatility: this.analyzeVolatility(timeSeries),
                weeklyPattern: this.analyzeWeeklyPattern(timeSeries),

                // Advanced patterns
                anomalies: this.detectAnomalies(timeSeries),
                changePoints: this.detectChangePoints(timeSeries),
                cyclicalPatterns: this.analyzeCyclicalPatterns(timeSeries),
                autocorrelation: this.calculateAutocorrelation(timeSeries),
                stationarity: this.testStationarity(timeSeries),

                // Forecasting
                predictions: null,
                confidence: null
            };

            // Generate predictions if model is available
            if (this.model && timeSeries.length >= this.sequenceLength) {
                const forecastResult = await this.generateAdvancedForecast(timeSeries);
                patterns.predictions = forecastResult.predictions;
                patterns.confidence = forecastResult.confidence;
            }

            return patterns;
        } catch (error) {
            console.error('Error in advanced pattern analysis:', error);
            throw error;
        }
    }

    /**
     * Detect anomalies in time series data
     */
    detectAnomalies(timeSeries) {
        if (timeSeries.length < 3) {
            return { anomalies: [], anomalyCount: 0, anomalyRate: 0 };
        }

        const values = timeSeries.map(point => point.value);
        const mean = ss.mean(values);
        const stdDev = ss.standardDeviation(values);

        const anomalies = [];

        timeSeries.forEach((point, index) => {
            const zScore = Math.abs((point.value - mean) / stdDev);

            if (zScore > this.anomalyThreshold) {
                anomalies.push({
                    index,
                    date: point.date,
                    value: point.value,
                    zScore,
                    severity: zScore > 3 ? 'high' : 'medium'
                });
            }
        });

        return {
            anomalies,
            anomalyCount: anomalies.length,
            anomalyRate: anomalies.length / timeSeries.length,
            threshold: this.anomalyThreshold,
            statistics: { mean, stdDev }
        };
    }

    /**
     * Detect change points in the time series
     */
    detectChangePoints(timeSeries) {
        if (timeSeries.length < 6) {
            return { changePoints: [], changePointCount: 0 };
        }

        const values = timeSeries.map(point => point.value);
        const changePoints = [];
        const windowSize = Math.min(5, Math.floor(timeSeries.length / 3));

        for (let i = windowSize; i < values.length - windowSize; i++) {
            const beforeWindow = values.slice(i - windowSize, i);
            const afterWindow = values.slice(i, i + windowSize);

            const beforeMean = ss.mean(beforeWindow);
            const afterMean = ss.mean(afterWindow);
            const beforeStd = ss.standardDeviation(beforeWindow);
            const afterStd = ss.standardDeviation(afterWindow);

            // Calculate change magnitude
            const meanChange = Math.abs(afterMean - beforeMean);
            const stdChange = Math.abs(afterStd - beforeStd);
            const combinedStd = Math.sqrt((beforeStd ** 2 + afterStd ** 2) / 2);

            // Detect significant change
            if (meanChange > 1.5 * combinedStd && combinedStd > 0) {
                changePoints.push({
                    index: i,
                    date: timeSeries[i].date,
                    beforeMean,
                    afterMean,
                    changeDirection: afterMean > beforeMean ? 'increase' : 'decrease',
                    changeMagnitude: meanChange,
                    significance: meanChange / combinedStd
                });
            }
        }

        return {
            changePoints,
            changePointCount: changePoints.length,
            changeRate: changePoints.length / timeSeries.length
        };
    }

    /**
     * Analyze cyclical patterns beyond weekly patterns
     */
    analyzeCyclicalPatterns(timeSeries) {
        if (timeSeries.length < 14) {
            return { cycles: [], dominantCycle: null };
        }

        const values = timeSeries.map(point => point.value);
        const cycles = [];

        // Test different cycle lengths
        const cycleLengths = [7, 14, 21, 28, 30]; // Weekly, bi-weekly, monthly patterns

        cycleLengths.forEach(cycleLength => {
            if (timeSeries.length >= cycleLength * 2) {
                const correlation = this.calculateCyclicalCorrelation(values, cycleLength);

                if (correlation > 0.3) { // Threshold for significant correlation
                    cycles.push({
                        length: cycleLength,
                        correlation,
                        type: this.getCycleType(cycleLength),
                        strength: this.categorizeCycleStrength(correlation)
                    });
                }
            }
        });

        // Find dominant cycle
        const dominantCycle = cycles.length > 0
            ? cycles.reduce((max, cycle) => cycle.correlation > max.correlation ? cycle : max)
            : null;

        return {
            cycles: cycles.sort((a, b) => b.correlation - a.correlation),
            dominantCycle,
            cycleCount: cycles.length
        };
    }

    /**
     * Calculate cyclical correlation for a given cycle length
     */
    calculateCyclicalCorrelation(values, cycleLength) {
        if (values.length < cycleLength * 2) return 0;

        const cycles = [];
        for (let i = 0; i <= values.length - cycleLength; i += cycleLength) {
            cycles.push(values.slice(i, i + cycleLength));
        }

        if (cycles.length < 2) return 0;

        // Calculate correlation between cycles
        const correlations = [];
        for (let i = 0; i < cycles.length - 1; i++) {
            for (let j = i + 1; j < cycles.length; j++) {
                if (cycles[i].length === cycles[j].length) {
                    try {
                        const correlation = ss.sampleCorrelation(cycles[i], cycles[j]);
                        if (!isNaN(correlation)) {
                            correlations.push(correlation);
                        }
                    } catch (error) {
                        // Skip if correlation calculation fails
                    }
                }
            }
        }

        return correlations.length > 0 ? ss.mean(correlations) : 0;
    }

    /**
     * Get cycle type description
     */
    getCycleType(cycleLength) {
        switch (cycleLength) {
            case 7: return 'weekly';
            case 14: return 'bi-weekly';
            case 21: return 'tri-weekly';
            case 28: return 'monthly';
            case 30: return 'calendar-monthly';
            default: return `${cycleLength}-day`;
        }
    }

    /**
     * Categorize cycle strength
     */
    categorizeCycleStrength(correlation) {
        if (correlation >= 0.7) return 'strong';
        if (correlation >= 0.5) return 'moderate';
        if (correlation >= 0.3) return 'weak';
        return 'negligible';
    }

    /**
     * Calculate autocorrelation function
     */
    calculateAutocorrelation(timeSeries, maxLag = null) {
        const values = timeSeries.map(point => point.value);
        const n = values.length;

        if (n < 3) {
            return { autocorrelations: [], significantLags: [], maxAutocorr: 0 };
        }

        maxLag = maxLag || Math.min(Math.floor(n / 4), 20);
        const autocorrelations = [];

        const mean = ss.mean(values);
        const variance = ss.variance(values);

        for (let lag = 0; lag <= maxLag; lag++) {
            let sum = 0;
            let count = 0;

            for (let i = 0; i < n - lag; i++) {
                sum += (values[i] - mean) * (values[i + lag] - mean);
                count++;
            }

            const autocorr = count > 0 ? sum / (count * variance) : 0;
            autocorrelations.push({ lag, value: autocorr });
        }

        // Find significant lags (above 95% confidence interval)
        const confidenceThreshold = 1.96 / Math.sqrt(n);
        const significantLags = autocorrelations.filter(
            (ac, index) => index > 0 && Math.abs(ac.value) > confidenceThreshold
        );

        const maxAutocorr = Math.max(...autocorrelations.slice(1).map(ac => Math.abs(ac.value)));

        return {
            autocorrelations,
            significantLags,
            maxAutocorr,
            confidenceThreshold
        };
    }

    /**
     * Test for stationarity using simple statistical tests
     */
    testStationarity(timeSeries) {
        const values = timeSeries.map(point => point.value);
        const n = values.length;

        if (n < 6) {
            return { isStationary: null, tests: {}, message: 'Insufficient data for stationarity test' };
        }

        // Split into two halves and compare
        const midPoint = Math.floor(n / 2);
        const firstHalf = values.slice(0, midPoint);
        const secondHalf = values.slice(midPoint);

        const firstMean = ss.mean(firstHalf);
        const secondMean = ss.mean(secondHalf);
        const firstVar = ss.variance(firstHalf);
        const secondVar = ss.variance(secondHalf);

        // Simple tests
        const meanDifference = Math.abs(firstMean - secondMean);
        const varianceDifference = Math.abs(firstVar - secondVar);
        const overallStd = ss.standardDeviation(values);

        // Heuristic thresholds
        const meanStable = meanDifference < (0.2 * overallStd);
        const varianceStable = varianceDifference < (0.3 * Math.max(firstVar, secondVar));

        // Trend test
        const trendTest = this.analyzeTrend(timeSeries);
        const trendStable = Math.abs(trendTest.slope) < 0.01;

        const isStationary = meanStable && varianceStable && trendStable;

        return {
            isStationary,
            tests: {
                meanStable,
                varianceStable,
                trendStable,
                meanDifference,
                varianceDifference,
                trendSlope: trendTest.slope
            },
            confidence: isStationary ? 'medium' : 'low' // Simple heuristic
        };
    }

    /**
     * Generate advanced forecast with confidence intervals
     */
    async generateAdvancedForecast(timeSeries) {
        if (!this.model || timeSeries.length < this.sequenceLength) {
            return { predictions: null, confidence: null, error: 'Model not available or insufficient data' };
        }

        try {
            // Prepare input sequence
            const recentData = timeSeries.slice(-this.sequenceLength);
            const inputSequence = recentData.map(point => point.value);

            // Normalize input
            const mean = ss.mean(inputSequence);
            const std = ss.standardDeviation(inputSequence) || 1;
            const normalizedInput = inputSequence.map(val => (val - mean) / std);

            // Create tensor
            const inputTensor = tf.tensor3d([normalizedInput.map(val => [val])]);

            // Generate prediction
            const prediction = this.model.predict(inputTensor);
            const predictionData = await prediction.data();

            // Denormalize predictions
            const denormalizedPredictions = Array.from(predictionData).map(val => val * std + mean);

            // Generate prediction dates
            const lastDate = new Date(timeSeries[timeSeries.length - 1].date);
            const predictions = denormalizedPredictions.map((value, index) => ({
                date: format(addDays(lastDate, index + 1), 'yyyy-MM-dd'),
                value: Math.max(0, Math.min(1, value)), // Clamp between 0 and 1
                dayIndex: index + 1
            }));

            // Calculate confidence based on recent model performance and data variability
            const recentVariability = ss.standardDeviation(inputSequence);
            const confidence = Math.max(0.3, Math.min(0.9, 1 - (recentVariability * 2)));

            // Clean up tensors
            inputTensor.dispose();
            prediction.dispose();

            return {
                predictions,
                confidence,
                metadata: {
                    inputLength: this.sequenceLength,
                    predictionHorizon: this.predictionHorizon,
                    normalizationStats: { mean, std },
                    recentVariability
                }
            };
        } catch (error) {
            console.error('Error generating advanced forecast:', error);
            return { predictions: null, confidence: null, error: error.message };
        }
    }

    /**
     * Evaluate model performance on historical data
     */
    async evaluateModelPerformance(testData) {
        if (!this.model || testData.length === 0) {
            return { error: 'Model not available or no test data' };
        }

        try {
            const { sequences, targets } = this.prepareSequences(testData);

            if (sequences.length === 0) {
                return { error: 'Insufficient test data' };
            }

            // Convert to tensors
            const xs = tf.tensor3d(sequences);
            const ys = tf.tensor2d(targets);

            // Evaluate model
            const evaluation = this.model.evaluate(xs, ys);
            const loss = await evaluation[0].data();
            const mae = await evaluation[1].data();

            // Calculate additional metrics
            const predictions = this.model.predict(xs);
            const predictionData = await predictions.data();
            const targetData = await ys.data();

            // Calculate R-squared
            const rSquared = this.calculateRSquared(Array.from(targetData), Array.from(predictionData));

            // Clean up tensors
            xs.dispose();
            ys.dispose();
            evaluation[0].dispose();
            evaluation[1].dispose();
            predictions.dispose();

            return {
                loss: loss[0],
                mae: mae[0],
                rSquared,
                sampleSize: sequences.length,
                evaluationDate: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error evaluating model performance:', error);
            return { error: error.message };
        }
    }

    /**
     * Calculate R-squared metric
     */
    calculateRSquared(actual, predicted) {
        if (actual.length !== predicted.length || actual.length === 0) {
            return 0;
        }

        const actualMean = ss.mean(actual);
        const totalSumSquares = actual.reduce((sum, val) => sum + Math.pow(val - actualMean, 2), 0);
        const residualSumSquares = actual.reduce((sum, val, i) => sum + Math.pow(val - predicted[i], 2), 0);

        return totalSumSquares > 0 ? 1 - (residualSumSquares / totalSumSquares) : 0;
    }

    /**
     * Get model training metrics
     */
    getModelMetrics() {
        return {
            ...this.modelMetrics,
            isModelTrained: this.model !== null,
            sequenceLength: this.sequenceLength,
            predictionHorizon: this.predictionHorizon
        };
    }

    /**
     * Save model to file (placeholder - would need actual file system implementation)
     */
    async saveModel(modelPath) {
        if (!this.model) {
            throw new Error('No model to save');
        }

        try {
            // In a real implementation, you would save to file system
            // await this.model.save(`file://${modelPath}`);
            console.log(`Model would be saved to: ${modelPath}`);
            return { success: true, path: modelPath };
        } catch (error) {
            console.error('Error saving model:', error);
            throw error;
        }
    }

    /**
     * Load model from file (placeholder - would need actual file system implementation)
     */
    async loadModel(modelPath) {
        try {
            // In a real implementation, you would load from file system
            // this.model = await tf.loadLayersModel(`file://${modelPath}`);
            console.log(`Model would be loaded from: ${modelPath}`);
            return { success: true, path: modelPath };
        } catch (error) {
            console.error('Error loading model:', error);
            throw error;
        }
    }
}

module.exports = TimeSeriesAnalyzer;
