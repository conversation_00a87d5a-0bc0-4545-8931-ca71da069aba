const { format, parseISO, differenceInDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isValid } = require('date-fns');
const ss = require('simple-statistics');
const DatabaseService = require('./DatabaseService');

/**
 * Data Preprocessor for AI Analytics
 * Handles data cleaning, transformation, and feature engineering
 */
class DataPreprocessor {
    constructor() {
        this.initialized = false;
        this.dbService = new DatabaseService();
        this.dataQualityThresholds = {
            minRecordsPerStudent: 5,
            maxMissingDataPercentage: 0.3,
            minDateRange: 7 // days
        };
    }

    /**
     * Initialize the data preprocessor
     */
    async initialize() {
        this.dbService.initialize();
        this.initialized = true;
        console.log('Data Preprocessor initialized');
    }

    /**
     * Close database connections
     */
    close() {
        if (this.dbService) {
            this.dbService.close();
        }
    }

    // ============================================================================
    // DATA EXTRACTION METHODS
    // ============================================================================

    /**
     * Extract attendance data for AI analysis
     */
    async extractAttendanceData(options = {}) {
        try {
            const {
                studentIds = null,
                dateFrom = null,
                dateTo = null,
                includeSubjects = true,
                includeTeachers = true
            } = options;

            console.log('Extracting attendance data for AI analysis...');

            const rawData = this.dbService.getAttendanceDataForAnalysis(
                studentIds,
                dateFrom,
                dateTo
            );

            console.log(`Extracted ${rawData.length} attendance records`);
            return rawData;
        } catch (error) {
            console.error('Error extracting attendance data:', error);
            throw error;
        }
    }

    /**
     * Extract performance data for correlation analysis
     */
    async extractPerformanceData(studentIds = null) {
        try {
            console.log('Extracting performance data for correlation analysis...');

            const performanceData = this.dbService.getPerformanceDataForAnalysis(studentIds);

            console.log(`Extracted ${performanceData.length} performance records`);
            return performanceData;
        } catch (error) {
            console.error('Error extracting performance data:', error);
            throw error;
        }
    }

    // ============================================================================
    // DATA CLEANING AND VALIDATION METHODS
    // ============================================================================

    /**
     * Clean and validate attendance data
     */
    cleanAttendanceData(rawData) {
        console.log('Cleaning attendance data...');

        let cleanedData = rawData.filter(record => this.isValidAttendanceRecord(record));

        // Remove duplicates
        cleanedData = this.removeDuplicateRecords(cleanedData);

        // Standardize status values
        cleanedData = cleanedData.map(record => this.standardizeAttendanceRecord(record));

        // Sort by student and date
        cleanedData.sort((a, b) => {
            if (a.student_id !== b.student_id) {
                return a.student_id - b.student_id;
            }
            return new Date(a.date_time) - new Date(b.date_time);
        });

        console.log(`Cleaned data: ${rawData.length} -> ${cleanedData.length} records`);
        return cleanedData;
    }

    /**
     * Validate individual attendance record
     */
    isValidAttendanceRecord(record) {
        // Check required fields
        if (!record.student_id || !record.session_id || !record.status) {
            return false;
        }

        // Check date validity
        if (!record.date_time || !isValid(new Date(record.date_time))) {
            return false;
        }

        // Check status validity
        const validStatuses = ['present', 'late', 'absent'];
        if (!validStatuses.includes(record.status.toLowerCase())) {
            return false;
        }

        return true;
    }

    /**
     * Remove duplicate attendance records
     */
    removeDuplicateRecords(data) {
        const seen = new Set();
        return data.filter(record => {
            const key = `${record.student_id}-${record.session_id}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    /**
     * Standardize attendance record format
     */
    standardizeAttendanceRecord(record) {
        return {
            ...record,
            status: record.status.toLowerCase(),
            date_time: new Date(record.date_time).toISOString(),
            student_name: `${record.first_name || ''} ${record.last_name || ''}`.trim(),
            grade_section: `${record.grade_level || ''}-${record.section || ''}`.replace(/^-|-$/g, '')
        };
    }

    /**
     * Validate data quality for AI training
     */
    validateDataQuality(studentData) {
        const qualityReport = {
            totalStudents: Object.keys(studentData).length,
            validStudents: 0,
            issues: []
        };

        Object.entries(studentData).forEach(([studentId, data]) => {
            const issues = this.checkStudentDataQuality(data);

            if (issues.length === 0) {
                qualityReport.validStudents++;
            } else {
                qualityReport.issues.push({
                    studentId,
                    studentName: data.studentInfo?.name,
                    issues
                });
            }
        });

        qualityReport.qualityScore = qualityReport.validStudents / qualityReport.totalStudents;

        console.log(`Data quality validation: ${qualityReport.validStudents}/${qualityReport.totalStudents} students passed (${(qualityReport.qualityScore * 100).toFixed(1)}%)`);

        return qualityReport;
    }

    /**
     * Check data quality for individual student
     */
    checkStudentDataQuality(studentData) {
        const issues = [];
        const attendance = studentData.attendance || [];

        // Check minimum records
        if (attendance.length < this.dataQualityThresholds.minRecordsPerStudent) {
            issues.push(`Insufficient records: ${attendance.length} < ${this.dataQualityThresholds.minRecordsPerStudent}`);
        }

        // Check date range
        if (attendance.length > 0) {
            const dateRange = this.getDateRange(attendance);
            const daysDiff = differenceInDays(new Date(dateRange.end), new Date(dateRange.start));

            if (daysDiff < this.dataQualityThresholds.minDateRange) {
                issues.push(`Insufficient date range: ${daysDiff} days < ${this.dataQualityThresholds.minDateRange} days`);
            }
        }

        // Check for missing data patterns
        const missingDataPercentage = this.calculateMissingDataPercentage(attendance);
        if (missingDataPercentage > this.dataQualityThresholds.maxMissingDataPercentage) {
            issues.push(`High missing data: ${(missingDataPercentage * 100).toFixed(1)}% > ${(this.dataQualityThresholds.maxMissingDataPercentage * 100)}%`);
        }

        return issues;
    }

    /**
     * Calculate missing data percentage for a student
     */
    calculateMissingDataPercentage(attendance) {
        if (attendance.length === 0) return 1.0;

        // This is a simplified calculation - in practice, you'd compare against expected sessions
        const totalExpectedSessions = attendance.length; // Placeholder
        const actualSessions = attendance.length;

        return Math.max(0, (totalExpectedSessions - actualSessions) / totalExpectedSessions);
    }

    /**
     * Prepare training data from historical attendance records
     */
    async prepareTrainingData(historicalData) {
        try {
            console.log(`Preparing training data from ${historicalData.length} records`);
            
            // Group data by student
            const studentData = this.groupByStudent(historicalData);
            
            // Prepare time series data
            const timeSeries = this.prepareTimeSeriesData(studentData);
            
            // Prepare risk classification data
            const riskData = this.prepareRiskData(studentData);
            
            // Prepare clustering data
            const clusterData = this.prepareClusterData(studentData);
            
            return {
                timeSeries,
                riskData,
                clusterData,
                metadata: {
                    totalStudents: Object.keys(studentData).length,
                    totalRecords: historicalData.length,
                    dateRange: this.getDateRange(historicalData)
                }
            };
        } catch (error) {
            console.error('Error preparing training data:', error);
            throw error;
        }
    }

    /**
     * Preprocess data for a specific student
     */
    async preprocessStudentData(attendanceData) {
        try {
            // Calculate attendance features
            const features = this.calculateAttendanceFeatures(attendanceData);
            
            // Create time series
            const timeSeries = this.createStudentTimeSeries(attendanceData);
            
            // Calculate behavioral patterns
            const behavioralPatterns = this.calculateBehavioralPatterns(attendanceData);
            
            return {
                features,
                timeSeries,
                behavioralPatterns,
                metadata: {
                    totalRecords: attendanceData.length,
                    dateRange: this.getDateRange(attendanceData)
                }
            };
        } catch (error) {
            console.error('Error preprocessing student data:', error);
            throw error;
        }
    }

    /**
     * Group attendance data by student
     */
    groupByStudent(data) {
        const grouped = {};
        
        data.forEach(record => {
            const studentId = record.student_id;
            if (!grouped[studentId]) {
                grouped[studentId] = {
                    studentInfo: {
                        id: studentId,
                        name: `${record.first_name} ${record.last_name}`,
                        gradeLevel: record.grade_level,
                        section: record.section
                    },
                    attendance: []
                };
            }
            grouped[studentId].attendance.push(record);
        });
        
        return grouped;
    }

    /**
     * Prepare time series data for training
     */
    prepareTimeSeriesData(studentData) {
        const timeSeriesData = [];
        
        Object.values(studentData).forEach(student => {
            const timeSeries = this.createStudentTimeSeries(student.attendance);
            if (timeSeries.length > 0) {
                timeSeriesData.push({
                    studentId: student.studentInfo.id,
                    timeSeries,
                    features: this.calculateAttendanceFeatures(student.attendance)
                });
            }
        });
        
        return timeSeriesData;
    }

    /**
     * Prepare risk classification data
     */
    prepareRiskData(studentData) {
        const riskData = [];
        
        Object.values(studentData).forEach(student => {
            const features = this.calculateAttendanceFeatures(student.attendance);
            const riskLabel = this.calculateRiskLabel(features);
            
            riskData.push({
                studentId: student.studentInfo.id,
                features: Object.values(features),
                label: riskLabel
            });
        });
        
        return riskData;
    }

    /**
     * Prepare clustering data
     */
    prepareClusterData(studentData) {
        const clusterData = [];
        
        Object.values(studentData).forEach(student => {
            const features = this.calculateAttendanceFeatures(student.attendance);
            const behavioralPatterns = this.calculateBehavioralPatterns(student.attendance);
            
            clusterData.push({
                studentId: student.studentInfo.id,
                features: [
                    ...Object.values(features),
                    ...Object.values(behavioralPatterns)
                ]
            });
        });
        
        return clusterData;
    }

    /**
     * Calculate attendance features for a student
     */
    calculateAttendanceFeatures(attendanceData) {
        if (attendanceData.length === 0) {
            return this.getDefaultFeatures();
        }

        // Sort by date
        const sortedData = attendanceData.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
        
        // Calculate basic statistics
        const totalSessions = sortedData.length;
        const presentCount = sortedData.filter(a => a.status === 'present').length;
        const lateCount = sortedData.filter(a => a.status === 'late').length;
        const absentCount = sortedData.filter(a => a.status === 'absent').length;
        
        // Calculate rates
        const attendanceRate = totalSessions > 0 ? presentCount / totalSessions : 0;
        const lateRate = totalSessions > 0 ? lateCount / totalSessions : 0;
        const absentRate = totalSessions > 0 ? absentCount / totalSessions : 0;
        
        // Calculate trends
        const recentTrend = this.calculateRecentTrend(sortedData);
        const weeklyPattern = this.calculateWeeklyPattern(sortedData);
        const monthlyPattern = this.calculateMonthlyPattern(sortedData);
        
        // Calculate consistency metrics
        const consistency = this.calculateConsistency(sortedData);
        const streaks = this.calculateStreaks(sortedData);
        
        return {
            totalSessions,
            attendanceRate,
            lateRate,
            absentRate,
            recentTrend,
            weeklyVariability: weeklyPattern.variability,
            monthlyVariability: monthlyPattern.variability,
            consistency,
            longestAbsentStreak: streaks.longestAbsent,
            longestPresentStreak: streaks.longestPresent,
            averageGapBetweenAbsences: streaks.averageGap
        };
    }

    /**
     * Create time series for a student
     */
    createStudentTimeSeries(attendanceData) {
        if (attendanceData.length === 0) return [];
        
        // Sort by date
        const sortedData = attendanceData.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
        
        // Create daily attendance series
        const timeSeries = sortedData.map(record => ({
            date: format(new Date(record.created_at), 'yyyy-MM-dd'),
            status: record.status,
            value: this.statusToNumeric(record.status),
            dayOfWeek: new Date(record.created_at).getDay(),
            weekOfYear: this.getWeekOfYear(new Date(record.created_at))
        }));
        
        return timeSeries;
    }

    /**
     * Calculate behavioral patterns
     */
    calculateBehavioralPatterns(attendanceData) {
        if (attendanceData.length === 0) {
            return {
                morningAttendance: 0,
                afternoonAttendance: 0,
                mondayPattern: 0,
                fridayPattern: 0,
                seasonalVariation: 0
            };
        }

        // Analyze time-based patterns
        const morningRecords = attendanceData.filter(a => {
            const hour = new Date(a.session_date || a.created_at).getHours();
            return hour < 12;
        });
        
        const afternoonRecords = attendanceData.filter(a => {
            const hour = new Date(a.session_date || a.created_at).getHours();
            return hour >= 12;
        });
        
        // Analyze day-of-week patterns
        const mondayRecords = attendanceData.filter(a => new Date(a.created_at).getDay() === 1);
        const fridayRecords = attendanceData.filter(a => new Date(a.created_at).getDay() === 5);
        
        return {
            morningAttendance: this.calculateAttendanceRate(morningRecords),
            afternoonAttendance: this.calculateAttendanceRate(afternoonRecords),
            mondayPattern: this.calculateAttendanceRate(mondayRecords),
            fridayPattern: this.calculateAttendanceRate(fridayRecords),
            seasonalVariation: this.calculateSeasonalVariation(attendanceData)
        };
    }

    /**
     * Calculate risk label based on features
     */
    calculateRiskLabel(features) {
        const { attendanceRate, absentRate, longestAbsentStreak, recentTrend } = features;
        
        // High risk criteria
        if (attendanceRate < 0.7 || absentRate > 0.3 || longestAbsentStreak > 5 || recentTrend < -0.2) {
            return 'high';
        }
        
        // Medium risk criteria
        if (attendanceRate < 0.85 || absentRate > 0.15 || longestAbsentStreak > 3 || recentTrend < -0.1) {
            return 'medium';
        }
        
        return 'low';
    }

    /**
     * Helper methods
     */
    getDefaultFeatures() {
        return {
            totalSessions: 0,
            attendanceRate: 0,
            lateRate: 0,
            absentRate: 0,
            recentTrend: 0,
            weeklyVariability: 0,
            monthlyVariability: 0,
            consistency: 0,
            longestAbsentStreak: 0,
            longestPresentStreak: 0,
            averageGapBetweenAbsences: 0
        };
    }

    statusToNumeric(status) {
        switch (status) {
            case 'present': return 1;
            case 'late': return 0.5;
            case 'absent': return 0;
            default: return 0;
        }
    }

    calculateAttendanceRate(records) {
        if (records.length === 0) return 0;
        const presentCount = records.filter(r => r.status === 'present').length;
        return presentCount / records.length;
    }

    calculateRecentTrend(sortedData) {
        if (sortedData.length < 5) return 0;
        
        const recent = sortedData.slice(-10); // Last 10 records
        const values = recent.map(r => this.statusToNumeric(r.status));
        
        // Simple linear trend calculation
        const n = values.length;
        const x = Array.from({length: n}, (_, i) => i);
        const slope = ss.linearRegressionLine(ss.linearRegression(x.map((xi, i) => [xi, values[i]])));
        
        return slope(n) - slope(0);
    }

    calculateWeeklyPattern(data) {
        // Group by week and calculate variability
        const weeklyData = {};
        data.forEach(record => {
            const week = this.getWeekOfYear(new Date(record.created_at));
            if (!weeklyData[week]) weeklyData[week] = [];
            weeklyData[week].push(this.statusToNumeric(record.status));
        });
        
        const weeklyAverages = Object.values(weeklyData).map(week => ss.mean(week));
        return {
            variability: weeklyAverages.length > 1 ? ss.standardDeviation(weeklyAverages) : 0
        };
    }

    calculateMonthlyPattern(data) {
        // Group by month and calculate variability
        const monthlyData = {};
        data.forEach(record => {
            const month = format(new Date(record.created_at), 'yyyy-MM');
            if (!monthlyData[month]) monthlyData[month] = [];
            monthlyData[month].push(this.statusToNumeric(record.status));
        });
        
        const monthlyAverages = Object.values(monthlyData).map(month => ss.mean(month));
        return {
            variability: monthlyAverages.length > 1 ? ss.standardDeviation(monthlyAverages) : 0
        };
    }

    calculateConsistency(data) {
        const values = data.map(r => this.statusToNumeric(r.status));
        return values.length > 1 ? 1 - ss.standardDeviation(values) : 1;
    }

    calculateStreaks(data) {
        let longestAbsent = 0;
        let longestPresent = 0;
        let currentAbsent = 0;
        let currentPresent = 0;
        const gaps = [];
        let lastAbsentIndex = -1;
        
        data.forEach((record, index) => {
            if (record.status === 'absent') {
                currentAbsent++;
                currentPresent = 0;
                
                if (lastAbsentIndex >= 0) {
                    gaps.push(index - lastAbsentIndex);
                }
                lastAbsentIndex = index;
            } else {
                currentPresent++;
                currentAbsent = 0;
            }
            
            longestAbsent = Math.max(longestAbsent, currentAbsent);
            longestPresent = Math.max(longestPresent, currentPresent);
        });
        
        return {
            longestAbsent,
            longestPresent,
            averageGap: gaps.length > 0 ? ss.mean(gaps) : 0
        };
    }

    calculateSeasonalVariation(data) {
        // Simple seasonal variation based on month
        const monthlyData = {};
        data.forEach(record => {
            const month = new Date(record.created_at).getMonth();
            if (!monthlyData[month]) monthlyData[month] = [];
            monthlyData[month].push(this.statusToNumeric(record.status));
        });
        
        const monthlyAverages = Object.values(monthlyData).map(month => ss.mean(month));
        return monthlyAverages.length > 1 ? ss.standardDeviation(monthlyAverages) : 0;
    }

    getWeekOfYear(date) {
        const start = new Date(date.getFullYear(), 0, 1);
        const diff = date - start;
        return Math.ceil(diff / (7 * 24 * 60 * 60 * 1000));
    }

    getDateRange(data) {
        if (data.length === 0) return null;

        const dates = data.map(r => new Date(r.created_at || r.date_time));
        return {
            start: format(new Date(Math.min(...dates)), 'yyyy-MM-dd'),
            end: format(new Date(Math.max(...dates)), 'yyyy-MM-dd')
        };
    }

    // ============================================================================
    // DATA TRANSFORMATION METHODS
    // ============================================================================

    /**
     * Transform attendance data for different ML models
     */
    transformForModel(data, modelType) {
        switch (modelType) {
            case 'time_series':
                return this.transformForTimeSeries(data);
            case 'risk_classification':
                return this.transformForRiskClassification(data);
            case 'clustering':
                return this.transformForClustering(data);
            case 'correlation':
                return this.transformForCorrelation(data);
            default:
                throw new Error(`Unknown model type: ${modelType}`);
        }
    }

    /**
     * Transform data for time series analysis
     */
    transformForTimeSeries(studentData) {
        return Object.values(studentData).map(student => {
            const timeSeries = this.createStudentTimeSeries(student.attendance);
            const features = this.calculateAttendanceFeatures(student.attendance);

            return {
                studentId: student.studentInfo.id,
                studentName: student.studentInfo.name,
                timeSeries: timeSeries,
                features: features,
                metadata: {
                    gradeLevel: student.studentInfo.gradeLevel,
                    section: student.studentInfo.section,
                    recordCount: student.attendance.length
                }
            };
        }).filter(item => item.timeSeries.length > 0);
    }

    /**
     * Transform data for risk classification
     */
    transformForRiskClassification(studentData) {
        return Object.values(studentData).map(student => {
            const features = this.calculateAttendanceFeatures(student.attendance);
            const riskLabel = this.calculateRiskLabel(features);

            // Convert features object to array for ML model
            const featureArray = [
                features.totalSessions,
                features.attendanceRate,
                features.lateRate,
                features.absentRate,
                features.recentTrend,
                features.weeklyVariability,
                features.monthlyVariability,
                features.consistency,
                features.longestAbsentStreak,
                features.longestPresentStreak,
                features.averageGapBetweenAbsences
            ];

            return {
                studentId: student.studentInfo.id,
                studentName: student.studentInfo.name,
                features: featureArray,
                label: riskLabel,
                metadata: {
                    gradeLevel: student.studentInfo.gradeLevel,
                    section: student.studentInfo.section,
                    recordCount: student.attendance.length
                }
            };
        }).filter(item => item.features.every(f => !isNaN(f)));
    }

    /**
     * Transform data for clustering analysis
     */
    transformForClustering(studentData) {
        return Object.values(studentData).map(student => {
            const features = this.calculateAttendanceFeatures(student.attendance);
            const behavioralPatterns = this.calculateBehavioralPatterns(student.attendance);

            // Combine attendance features with behavioral patterns
            const combinedFeatures = [
                features.attendanceRate,
                features.lateRate,
                features.absentRate,
                features.consistency,
                features.recentTrend,
                features.weeklyVariability,
                features.monthlyVariability,
                behavioralPatterns.morningAttendance,
                behavioralPatterns.afternoonAttendance,
                behavioralPatterns.mondayPattern,
                behavioralPatterns.fridayPattern
            ];

            return {
                studentId: student.studentInfo.id,
                studentName: student.studentInfo.name,
                features: combinedFeatures,
                metadata: {
                    gradeLevel: student.studentInfo.gradeLevel,
                    section: student.studentInfo.section,
                    recordCount: student.attendance.length
                }
            };
        }).filter(item => item.features.every(f => !isNaN(f)));
    }

    /**
     * Transform data for correlation analysis
     */
    transformForCorrelation(attendanceData, performanceData) {
        const correlationData = [];

        // Group performance data by student
        const performanceByStudent = {};
        performanceData.forEach(record => {
            if (!performanceByStudent[record.student_id]) {
                performanceByStudent[record.student_id] = [];
            }
            performanceByStudent[record.student_id].push(record);
        });

        // Combine attendance and performance data
        Object.values(attendanceData).forEach(student => {
            const studentId = student.studentInfo.id;
            const performanceRecords = performanceByStudent[studentId] || [];

            if (performanceRecords.length > 0) {
                const attendanceFeatures = this.calculateAttendanceFeatures(student.attendance);
                const performanceFeatures = this.calculatePerformanceFeatures(performanceRecords);

                correlationData.push({
                    studentId: studentId,
                    studentName: student.studentInfo.name,
                    attendanceFeatures: attendanceFeatures,
                    performanceFeatures: performanceFeatures,
                    metadata: {
                        gradeLevel: student.studentInfo.gradeLevel,
                        section: student.studentInfo.section,
                        attendanceRecords: student.attendance.length,
                        performanceRecords: performanceRecords.length
                    }
                });
            }
        });

        return correlationData;
    }

    /**
     * Calculate performance features from performance records
     */
    calculatePerformanceFeatures(performanceRecords) {
        if (performanceRecords.length === 0) {
            return {
                averageGrade: 0,
                gradeVariability: 0,
                improvementTrend: 0,
                highestGrade: 0,
                lowestGrade: 0
            };
        }

        const grades = performanceRecords
            .map(r => parseFloat(r.grade || r.score || 0))
            .filter(g => !isNaN(g) && g > 0);

        if (grades.length === 0) {
            return {
                averageGrade: 0,
                gradeVariability: 0,
                improvementTrend: 0,
                highestGrade: 0,
                lowestGrade: 0
            };
        }

        const averageGrade = ss.mean(grades);
        const gradeVariability = grades.length > 1 ? ss.standardDeviation(grades) : 0;

        // Calculate improvement trend
        let improvementTrend = 0;
        if (grades.length > 2) {
            const indices = Array.from({ length: grades.length }, (_, i) => i);
            try {
                const regression = ss.linearRegression(indices.map((x, i) => [x, grades[i]]));
                improvementTrend = regression.m;
            } catch (error) {
                improvementTrend = 0;
            }
        }

        return {
            averageGrade,
            gradeVariability,
            improvementTrend,
            highestGrade: Math.max(...grades),
            lowestGrade: Math.min(...grades)
        };
    }

    // ============================================================================
    // FEATURE ENGINEERING METHODS
    // ============================================================================

    /**
     * Create advanced features for ML models
     */
    createAdvancedFeatures(studentData) {
        return Object.values(studentData).map(student => {
            const basicFeatures = this.calculateAttendanceFeatures(student.attendance);
            const behavioralFeatures = this.calculateBehavioralPatterns(student.attendance);
            const temporalFeatures = this.calculateTemporalFeatures(student.attendance);
            const streakFeatures = this.calculateStreakFeatures(student.attendance);

            return {
                studentId: student.studentInfo.id,
                features: {
                    ...basicFeatures,
                    ...behavioralFeatures,
                    ...temporalFeatures,
                    ...streakFeatures
                }
            };
        });
    }

    /**
     * Calculate temporal features
     */
    calculateTemporalFeatures(attendance) {
        if (attendance.length === 0) {
            return {
                seasonalPattern: 0,
                weekdayPreference: 0,
                timeOfDayPattern: 0,
                monthlyTrend: 0
            };
        }

        // Seasonal pattern (simplified)
        const seasonalAttendance = this.calculateSeasonalAttendance(attendance);

        // Weekday preference
        const weekdayAttendance = this.calculateWeekdayAttendance(attendance);

        return {
            seasonalPattern: seasonalAttendance.variance,
            weekdayPreference: weekdayAttendance.preference,
            timeOfDayPattern: 0, // Placeholder - would need time data
            monthlyTrend: this.calculateMonthlyVariability(attendance)
        };
    }

    /**
     * Calculate seasonal attendance patterns
     */
    calculateSeasonalAttendance(attendance) {
        const seasonalData = { spring: [], summer: [], fall: [], winter: [] };

        attendance.forEach(record => {
            const date = new Date(record.date_time);
            const month = date.getMonth() + 1;
            const value = this.statusToNumeric(record.status);

            if (month >= 3 && month <= 5) seasonalData.spring.push(value);
            else if (month >= 6 && month <= 8) seasonalData.summer.push(value);
            else if (month >= 9 && month <= 11) seasonalData.fall.push(value);
            else seasonalData.winter.push(value);
        });

        const seasonalAverages = Object.values(seasonalData)
            .filter(season => season.length > 0)
            .map(season => ss.mean(season));

        return {
            variance: seasonalAverages.length > 1 ? ss.variance(seasonalAverages) : 0,
            averages: seasonalAverages
        };
    }

    /**
     * Calculate weekday attendance patterns
     */
    calculateWeekdayAttendance(attendance) {
        const weekdayData = Array(7).fill(0).map(() => []);

        attendance.forEach(record => {
            const date = new Date(record.date_time);
            const weekday = date.getDay();
            weekdayData[weekday].push(this.statusToNumeric(record.status));
        });

        const weekdayAverages = weekdayData.map(day =>
            day.length > 0 ? ss.mean(day) : 0
        );

        const maxAvg = Math.max(...weekdayAverages);
        const minAvg = Math.min(...weekdayAverages);

        return {
            preference: maxAvg - minAvg,
            averages: weekdayAverages
        };
    }

    /**
     * Calculate streak-based features
     */
    calculateStreakFeatures(attendance) {
        if (attendance.length === 0) {
            return {
                currentStreak: 0,
                currentStreakType: 'none',
                averageStreakLength: 0,
                streakVariability: 0
            };
        }

        const streaks = this.calculateAllStreaks(attendance);
        const currentStreak = this.getCurrentStreak(attendance);

        const streakLengths = streaks.map(s => s.length);

        return {
            currentStreak: currentStreak.length,
            currentStreakType: currentStreak.type,
            averageStreakLength: streakLengths.length > 0 ? ss.mean(streakLengths) : 0,
            streakVariability: streakLengths.length > 1 ? ss.standardDeviation(streakLengths) : 0
        };
    }

    /**
     * Calculate all streaks in attendance data
     */
    calculateAllStreaks(attendance) {
        const streaks = [];
        let currentStreak = null;

        attendance.forEach(record => {
            const status = record.status;

            if (!currentStreak || currentStreak.type !== status) {
                if (currentStreak) {
                    streaks.push(currentStreak);
                }
                currentStreak = { type: status, length: 1, start: record.date_time };
            } else {
                currentStreak.length++;
            }
        });

        if (currentStreak) {
            streaks.push(currentStreak);
        }

        return streaks;
    }

    /**
     * Get current streak information
     */
    getCurrentStreak(attendance) {
        if (attendance.length === 0) {
            return { type: 'none', length: 0 };
        }

        const sortedAttendance = [...attendance].sort((a, b) =>
            new Date(b.date_time) - new Date(a.date_time)
        );

        const latestStatus = sortedAttendance[0].status;
        let streakLength = 1;

        for (let i = 1; i < sortedAttendance.length; i++) {
            if (sortedAttendance[i].status === latestStatus) {
                streakLength++;
            } else {
                break;
            }
        }

        return { type: latestStatus, length: streakLength };
    }
}

module.exports = DataPreprocessor;
