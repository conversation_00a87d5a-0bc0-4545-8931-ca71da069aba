-- Migration: Add AI Analytics Tables
-- Version: 1.0
-- Date: 2025-01-26
-- Description: Adds comprehensive AI analytics tables for attendance pattern analysis,
--              risk assessment, student clustering, and performance correlation

-- Check if migration has already been applied
CREATE TABLE IF NOT EXISTS migration_history (
    migration_id INTEGER PRIMARY KEY AUTOINCREMENT,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    version VARCHAR(20)
);

-- Record this migration
INSERT OR IGNORE INTO migration_history (migration_name, version) 
VALUES ('add_ai_analytics_tables', '1.0');

-- ============================================================================
-- AI ANALYTICS TABLES
-- ============================================================================

-- AI model metadata and configuration
CREATE TABLE IF NOT EXISTS ai_models (
    model_id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_name VARCHAR(100) NOT NULL UNIQUE,
    model_type VARCHAR(50) NOT NULL CHECK (model_type IN ('time_series', 'risk_classification', 'clustering', 'correlation')),
    model_version VARCHAR(20) NOT NULL,
    model_path VARCHAR(255), -- Path to saved model file
    training_data_hash VARCHAR(64), -- Hash of training data for versioning
    accuracy_score DECIMAL(5,4),
    training_date DATETIME,
    last_used DATETIME,
    is_active BOOLEAN DEFAULT 1,
    hyperparameters TEXT, -- JSON object with model hyperparameters
    feature_names TEXT, -- JSON array of feature names
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Student risk assessments and predictions
CREATE TABLE IF NOT EXISTS student_risk_assessments (
    assessment_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    assessment_date DATE NOT NULL,
    risk_level VARCHAR(20) NOT NULL CHECK (risk_level IN ('low', 'medium', 'high')),
    risk_score DECIMAL(5,4) NOT NULL, -- 0.0 to 1.0
    confidence_score DECIMAL(5,4) NOT NULL, -- 0.0 to 1.0
    model_id INTEGER,
    risk_factors TEXT, -- JSON array of risk factors
    recommendations TEXT, -- JSON array of recommendations
    intervention_status VARCHAR(20) DEFAULT 'none' CHECK (intervention_status IN ('none', 'planned', 'active', 'completed')),
    intervention_notes TEXT,
    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES ai_models(model_id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE(student_id, assessment_date)
);

-- Attendance pattern analysis results
CREATE TABLE IF NOT EXISTS attendance_patterns (
    pattern_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    analysis_date DATE NOT NULL,
    pattern_type VARCHAR(50) NOT NULL CHECK (pattern_type IN ('trend', 'seasonality', 'volatility', 'weekly', 'anomaly')),
    pattern_data TEXT NOT NULL, -- JSON object with pattern details
    strength DECIMAL(5,4), -- Pattern strength (0.0 to 1.0)
    significance DECIMAL(5,4), -- Statistical significance
    model_id INTEGER,
    timeframe_start DATE,
    timeframe_end DATE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES ai_models(model_id) ON DELETE SET NULL
);

-- Student behavioral clusters
CREATE TABLE IF NOT EXISTS student_clusters (
    cluster_assignment_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    cluster_id INTEGER NOT NULL,
    cluster_label VARCHAR(100) NOT NULL,
    assignment_date DATE NOT NULL,
    confidence_score DECIMAL(5,4) NOT NULL,
    distance_to_centroid DECIMAL(10,6),
    model_id INTEGER,
    cluster_characteristics TEXT, -- JSON object with cluster characteristics
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES ai_models(model_id) ON DELETE SET NULL,
    UNIQUE(student_id, assignment_date)
);

-- Attendance predictions
CREATE TABLE IF NOT EXISTS attendance_predictions (
    prediction_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    prediction_date DATE NOT NULL,
    target_date DATE NOT NULL, -- Date being predicted
    predicted_status VARCHAR(20) NOT NULL CHECK (predicted_status IN ('present', 'late', 'absent')),
    probability DECIMAL(5,4) NOT NULL, -- Probability of predicted status
    confidence_score DECIMAL(5,4) NOT NULL,
    model_id INTEGER,
    features_used TEXT, -- JSON object with features used for prediction
    actual_status VARCHAR(20) CHECK (actual_status IN ('present', 'late', 'absent')), -- Filled when actual data is available
    prediction_accuracy DECIMAL(5,4), -- Calculated when actual data is available
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES ai_models(model_id) ON DELETE SET NULL,
    UNIQUE(student_id, prediction_date, target_date)
);

-- Performance correlation analysis
CREATE TABLE IF NOT EXISTS performance_correlations (
    correlation_id INTEGER PRIMARY KEY AUTOINCREMENT,
    analysis_date DATE NOT NULL,
    correlation_type VARCHAR(50) NOT NULL CHECK (correlation_type IN ('overall', 'subject', 'grade_level', 'individual')),
    entity_id INTEGER, -- student_id for individual, subject_id for subject, etc.
    entity_type VARCHAR(20) CHECK (entity_type IN ('student', 'subject', 'grade', 'section')),
    correlation_coefficient DECIMAL(6,4) NOT NULL, -- -1.0 to 1.0
    correlation_strength VARCHAR(20) NOT NULL CHECK (correlation_strength IN ('negligible', 'weak', 'moderate', 'strong')),
    sample_size INTEGER NOT NULL,
    p_value DECIMAL(10,8), -- Statistical significance
    r_squared DECIMAL(5,4), -- Coefficient of determination
    analysis_details TEXT, -- JSON object with detailed analysis
    model_id INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (model_id) REFERENCES ai_models(model_id) ON DELETE SET NULL
);

-- Early warning alerts
CREATE TABLE IF NOT EXISTS early_warning_alerts (
    alert_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN ('risk_increase', 'pattern_change', 'prediction_concern', 'intervention_needed')),
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    alert_message TEXT NOT NULL,
    alert_data TEXT, -- JSON object with alert details
    triggered_by VARCHAR(50), -- What triggered the alert (model, rule, etc.)
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'acknowledged', 'resolved', 'dismissed')),
    acknowledged_by INTEGER,
    acknowledged_at DATETIME,
    resolved_by INTEGER,
    resolved_at DATETIME,
    resolution_notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (acknowledged_by) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- AI analytics job queue for background processing
CREATE TABLE IF NOT EXISTS ai_job_queue (
    job_id INTEGER PRIMARY KEY AUTOINCREMENT,
    job_type VARCHAR(50) NOT NULL CHECK (job_type IN ('model_training', 'risk_assessment', 'pattern_analysis', 'prediction_update', 'correlation_analysis')),
    job_status VARCHAR(20) DEFAULT 'pending' CHECK (job_status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    priority INTEGER DEFAULT 5 CHECK (priority BETWEEN 1 AND 10), -- 1 = highest, 10 = lowest
    job_data TEXT, -- JSON object with job parameters
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage BETWEEN 0 AND 100),
    result_data TEXT, -- JSON object with job results
    error_message TEXT,
    started_at DATETIME,
    completed_at DATETIME,
    estimated_duration INTEGER, -- Estimated duration in seconds
    actual_duration INTEGER, -- Actual duration in seconds
    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Model performance metrics and monitoring
CREATE TABLE IF NOT EXISTS model_performance_metrics (
    metric_id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_id INTEGER NOT NULL,
    metric_date DATE NOT NULL,
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('accuracy', 'precision', 'recall', 'f1_score', 'mse', 'mae', 'r_squared')),
    metric_value DECIMAL(10,6) NOT NULL,
    sample_size INTEGER,
    validation_type VARCHAR(20) CHECK (validation_type IN ('training', 'validation', 'test', 'production')),
    additional_metrics TEXT, -- JSON object with additional metrics
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (model_id) REFERENCES ai_models(model_id) ON DELETE CASCADE,
    UNIQUE(model_id, metric_date, metric_type, validation_type)
);

-- ============================================================================
-- AI ANALYTICS INDEXES
-- ============================================================================

-- AI Models indexes
CREATE INDEX IF NOT EXISTS idx_ai_models_type ON ai_models(model_type);
CREATE INDEX IF NOT EXISTS idx_ai_models_active ON ai_models(is_active);
CREATE INDEX IF NOT EXISTS idx_ai_models_training_date ON ai_models(training_date);
CREATE INDEX IF NOT EXISTS idx_ai_models_last_used ON ai_models(last_used);

-- Student Risk Assessments indexes
CREATE INDEX IF NOT EXISTS idx_risk_assessments_student ON student_risk_assessments(student_id);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_date ON student_risk_assessments(assessment_date);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_level ON student_risk_assessments(risk_level);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_score ON student_risk_assessments(risk_score);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_intervention ON student_risk_assessments(intervention_status);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_model ON student_risk_assessments(model_id);

-- Attendance Patterns indexes
CREATE INDEX IF NOT EXISTS idx_attendance_patterns_student ON attendance_patterns(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_patterns_date ON attendance_patterns(analysis_date);
CREATE INDEX IF NOT EXISTS idx_attendance_patterns_type ON attendance_patterns(pattern_type);
CREATE INDEX IF NOT EXISTS idx_attendance_patterns_timeframe ON attendance_patterns(timeframe_start, timeframe_end);
CREATE INDEX IF NOT EXISTS idx_attendance_patterns_model ON attendance_patterns(model_id);

-- Student Clusters indexes
CREATE INDEX IF NOT EXISTS idx_student_clusters_student ON student_clusters(student_id);
CREATE INDEX IF NOT EXISTS idx_student_clusters_cluster ON student_clusters(cluster_id);
CREATE INDEX IF NOT EXISTS idx_student_clusters_date ON student_clusters(assignment_date);
CREATE INDEX IF NOT EXISTS idx_student_clusters_label ON student_clusters(cluster_label);
CREATE INDEX IF NOT EXISTS idx_student_clusters_model ON student_clusters(model_id);

-- Attendance Predictions indexes
CREATE INDEX IF NOT EXISTS idx_attendance_predictions_student ON attendance_predictions(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_predictions_date ON attendance_predictions(prediction_date);
CREATE INDEX IF NOT EXISTS idx_attendance_predictions_target ON attendance_predictions(target_date);
CREATE INDEX IF NOT EXISTS idx_attendance_predictions_status ON attendance_predictions(predicted_status);
CREATE INDEX IF NOT EXISTS idx_attendance_predictions_model ON attendance_predictions(model_id);
CREATE INDEX IF NOT EXISTS idx_attendance_predictions_accuracy ON attendance_predictions(prediction_accuracy);

-- Performance Correlations indexes
CREATE INDEX IF NOT EXISTS idx_performance_correlations_date ON performance_correlations(analysis_date);
CREATE INDEX IF NOT EXISTS idx_performance_correlations_type ON performance_correlations(correlation_type);
CREATE INDEX IF NOT EXISTS idx_performance_correlations_entity ON performance_correlations(entity_id, entity_type);
CREATE INDEX IF NOT EXISTS idx_performance_correlations_coefficient ON performance_correlations(correlation_coefficient);
CREATE INDEX IF NOT EXISTS idx_performance_correlations_model ON performance_correlations(model_id);

-- Early Warning Alerts indexes
CREATE INDEX IF NOT EXISTS idx_early_warning_alerts_student ON early_warning_alerts(student_id);
CREATE INDEX IF NOT EXISTS idx_early_warning_alerts_type ON early_warning_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_early_warning_alerts_severity ON early_warning_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_early_warning_alerts_status ON early_warning_alerts(status);
CREATE INDEX IF NOT EXISTS idx_early_warning_alerts_created ON early_warning_alerts(created_at);

-- AI Job Queue indexes
CREATE INDEX IF NOT EXISTS idx_ai_job_queue_type ON ai_job_queue(job_type);
CREATE INDEX IF NOT EXISTS idx_ai_job_queue_status ON ai_job_queue(job_status);
CREATE INDEX IF NOT EXISTS idx_ai_job_queue_priority ON ai_job_queue(priority);
CREATE INDEX IF NOT EXISTS idx_ai_job_queue_created ON ai_job_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_job_queue_started ON ai_job_queue(started_at);

-- Model Performance Metrics indexes
CREATE INDEX IF NOT EXISTS idx_model_performance_model ON model_performance_metrics(model_id);
CREATE INDEX IF NOT EXISTS idx_model_performance_date ON model_performance_metrics(metric_date);
CREATE INDEX IF NOT EXISTS idx_model_performance_type ON model_performance_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_model_performance_validation ON model_performance_metrics(validation_type);

-- Insert initial AI model configurations
INSERT OR IGNORE INTO ai_models (model_name, model_type, model_version, is_active, created_at) VALUES
('TimeSeriesAnalyzer_v1', 'time_series', '1.0', 1, CURRENT_TIMESTAMP),
('RiskClassifier_v1', 'risk_classification', '1.0', 1, CURRENT_TIMESTAMP),
('StudentClusterer_v1', 'clustering', '1.0', 1, CURRENT_TIMESTAMP),
('PerformanceCorrelator_v1', 'correlation', '1.0', 1, CURRENT_TIMESTAMP);
