const tf = require('@tensorflow/tfjs');
const { Matrix } = require('ml-matrix');
const ss = require('simple-statistics');

/**
 * Student Clusterer for Behavioral Pattern Analysis
 * Groups students based on attendance behaviors and patterns
 */
class StudentClusterer {
    constructor() {
        this.model = null;
        this.centroids = null;
        this.featureScaler = null;
        this.numClusters = 5; // Configurable number of clusters
        this.clusterLabels = [
            'Excellent Attenders',
            'Consistent Attenders', 
            'Irregular Attenders',
            'Declining Attenders',
            'At-Risk Students'
        ];
    }

    /**
     * Train the clustering model
     */
    async trainModel(clusterData) {
        try {
            console.log('Training student clustering model...');
            
            if (clusterData.length === 0) {
                console.warn('No cluster data available for training');
                return null;
            }

            // Prepare training data
            const { features, scaler } = this.prepareClusteringData(clusterData);
            
            if (features.length === 0) {
                console.warn('Insufficient data for clustering training');
                return null;
            }

            this.featureScaler = scaler;
            
            // Perform K-means clustering
            const clusterResult = this.performKMeansClustering(features);
            this.centroids = clusterResult.centroids;
            
            // Analyze and label clusters
            this.analyzeClusterCharacteristics(features, clusterResult.assignments);
            
            console.log('Student clustering model trained successfully');
            return { centroids: this.centroids, assignments: clusterResult.assignments };
        } catch (error) {
            console.error('Error training clustering model:', error);
            throw error;
        }
    }

    /**
     * Prepare data for clustering
     */
    prepareClusteringData(clusterData) {
        const features = [];
        
        // Extract features
        clusterData.forEach(data => {
            if (data.features && data.features.length > 0) {
                features.push(data.features);
            }
        });
        
        if (features.length === 0) {
            return { features: [], scaler: null };
        }

        // Normalize features
        const scaler = this.createFeatureScaler(features);
        const normalizedFeatures = this.normalizeFeatures(features, scaler);
        
        return {
            features: normalizedFeatures,
            scaler
        };
    }

    /**
     * Create feature scaler for normalization
     */
    createFeatureScaler(features) {
        const numFeatures = features[0].length;
        const scaler = {
            means: [],
            stds: []
        };
        
        for (let i = 0; i < numFeatures; i++) {
            const featureValues = features.map(f => f[i]);
            scaler.means[i] = ss.mean(featureValues);
            scaler.stds[i] = ss.standardDeviation(featureValues) || 1;
        }
        
        return scaler;
    }

    /**
     * Normalize features using the scaler
     */
    normalizeFeatures(features, scaler) {
        return features.map(feature => 
            feature.map((value, index) => 
                (value - scaler.means[index]) / scaler.stds[index]
            )
        );
    }

    /**
     * Perform K-means clustering
     */
    performKMeansClustering(features) {
        const numFeatures = features[0].length;
        const numSamples = features.length;
        
        // Initialize centroids randomly
        let centroids = this.initializeCentroids(numFeatures);
        let assignments = new Array(numSamples).fill(0);
        let converged = false;
        let iterations = 0;
        const maxIterations = 100;
        
        while (!converged && iterations < maxIterations) {
            const newAssignments = new Array(numSamples);
            
            // Assign each point to nearest centroid
            for (let i = 0; i < numSamples; i++) {
                let minDistance = Infinity;
                let closestCentroid = 0;
                
                for (let j = 0; j < this.numClusters; j++) {
                    const distance = this.euclideanDistance(features[i], centroids[j]);
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestCentroid = j;
                    }
                }
                
                newAssignments[i] = closestCentroid;
            }
            
            // Update centroids
            const newCentroids = this.updateCentroids(features, newAssignments);
            
            // Check for convergence
            converged = this.checkConvergence(centroids, newCentroids);
            
            centroids = newCentroids;
            assignments = newAssignments;
            iterations++;
        }
        
        console.log(`K-means converged after ${iterations} iterations`);
        
        return {
            centroids,
            assignments,
            iterations
        };
    }

    /**
     * Initialize centroids randomly
     */
    initializeCentroids(numFeatures) {
        const centroids = [];
        
        for (let i = 0; i < this.numClusters; i++) {
            const centroid = [];
            for (let j = 0; j < numFeatures; j++) {
                centroid.push(Math.random() * 2 - 1); // Random value between -1 and 1
            }
            centroids.push(centroid);
        }
        
        return centroids;
    }

    /**
     * Calculate Euclidean distance between two points
     */
    euclideanDistance(point1, point2) {
        let sum = 0;
        for (let i = 0; i < point1.length; i++) {
            sum += Math.pow(point1[i] - point2[i], 2);
        }
        return Math.sqrt(sum);
    }

    /**
     * Update centroids based on current assignments
     */
    updateCentroids(features, assignments) {
        const numFeatures = features[0].length;
        const newCentroids = [];
        
        for (let i = 0; i < this.numClusters; i++) {
            const clusterPoints = features.filter((_, index) => assignments[index] === i);
            
            if (clusterPoints.length === 0) {
                // If no points assigned to this cluster, keep the old centroid
                newCentroids.push(new Array(numFeatures).fill(0));
                continue;
            }
            
            const centroid = new Array(numFeatures).fill(0);
            
            // Calculate mean for each feature
            for (let j = 0; j < numFeatures; j++) {
                const featureSum = clusterPoints.reduce((sum, point) => sum + point[j], 0);
                centroid[j] = featureSum / clusterPoints.length;
            }
            
            newCentroids.push(centroid);
        }
        
        return newCentroids;
    }

    /**
     * Check if centroids have converged
     */
    checkConvergence(oldCentroids, newCentroids, threshold = 0.001) {
        for (let i = 0; i < oldCentroids.length; i++) {
            const distance = this.euclideanDistance(oldCentroids[i], newCentroids[i]);
            if (distance > threshold) {
                return false;
            }
        }
        return true;
    }

    /**
     * Analyze cluster characteristics and assign meaningful labels
     */
    analyzeClusterCharacteristics(features, assignments) {
        this.clusterCharacteristics = [];
        
        for (let i = 0; i < this.numClusters; i++) {
            const clusterPoints = features.filter((_, index) => assignments[index] === i);
            
            if (clusterPoints.length === 0) {
                this.clusterCharacteristics.push({
                    label: `Empty Cluster ${i}`,
                    size: 0,
                    characteristics: {}
                });
                continue;
            }
            
            // Calculate cluster statistics
            const characteristics = this.calculateClusterStatistics(clusterPoints);
            const label = this.assignClusterLabel(characteristics, i);
            
            this.clusterCharacteristics.push({
                label,
                size: clusterPoints.length,
                characteristics
            });
        }
    }

    /**
     * Calculate statistics for a cluster
     */
    calculateClusterStatistics(clusterPoints) {
        const numFeatures = clusterPoints[0].length;
        const stats = {};
        
        // Assuming feature order: [totalSessions, attendanceRate, lateRate, absentRate, recentTrend, ...]
        const featureNames = [
            'totalSessions', 'attendanceRate', 'lateRate', 'absentRate', 'recentTrend',
            'weeklyVariability', 'monthlyVariability', 'consistency', 'longestAbsentStreak',
            'longestPresentStreak', 'averageGapBetweenAbsences'
        ];
        
        for (let i = 0; i < Math.min(numFeatures, featureNames.length); i++) {
            const featureValues = clusterPoints.map(point => point[i]);
            stats[featureNames[i]] = {
                mean: ss.mean(featureValues),
                std: ss.standardDeviation(featureValues),
                min: Math.min(...featureValues),
                max: Math.max(...featureValues)
            };
        }
        
        return stats;
    }

    /**
     * Assign meaningful label to cluster based on characteristics
     */
    assignClusterLabel(characteristics, clusterIndex) {
        const attendanceRate = characteristics.attendanceRate?.mean || 0;
        const absentRate = characteristics.absentRate?.mean || 0;
        const recentTrend = characteristics.recentTrend?.mean || 0;
        const consistency = characteristics.consistency?.mean || 0;
        
        // Excellent Attenders: High attendance, low absence, stable
        if (attendanceRate > 0.9 && absentRate < 0.1 && consistency > 0.8) {
            return 'Excellent Attenders';
        }
        
        // Consistent Attenders: Good attendance, stable patterns
        if (attendanceRate > 0.8 && absentRate < 0.2 && consistency > 0.6) {
            return 'Consistent Attenders';
        }
        
        // Declining Attenders: Negative trend
        if (recentTrend < -0.1) {
            return 'Declining Attenders';
        }
        
        // At-Risk Students: Low attendance, high absence
        if (attendanceRate < 0.7 || absentRate > 0.3) {
            return 'At-Risk Students';
        }
        
        // Default to Irregular Attenders
        return 'Irregular Attenders';
    }

    /**
     * Assign a student to a cluster
     */
    async assignCluster(preprocessedData) {
        if (!this.centroids || !this.featureScaler) {
            return {
                clusterId: null,
                clusterLabel: 'Unknown',
                confidence: 0,
                message: 'Clustering model not trained'
            };
        }

        try {
            const { features } = preprocessedData;
            
            // Convert features object to array (same order as training)
            const featureArray = [
                features.totalSessions || 0,
                features.attendanceRate || 0,
                features.lateRate || 0,
                features.absentRate || 0,
                features.recentTrend || 0,
                features.weeklyVariability || 0,
                features.monthlyVariability || 0,
                features.consistency || 0,
                features.longestAbsentStreak || 0,
                features.longestPresentStreak || 0,
                features.averageGapBetweenAbsences || 0
            ];
            
            // Normalize features
            const normalizedFeatures = this.normalizeFeatures([featureArray], this.featureScaler)[0];
            
            // Find closest centroid
            let minDistance = Infinity;
            let closestCluster = 0;
            const distances = [];
            
            for (let i = 0; i < this.centroids.length; i++) {
                const distance = this.euclideanDistance(normalizedFeatures, this.centroids[i]);
                distances.push(distance);
                
                if (distance < minDistance) {
                    minDistance = distance;
                    closestCluster = i;
                }
            }
            
            // Calculate confidence based on distance to centroid
            const maxDistance = Math.max(...distances);
            const confidence = maxDistance > 0 ? 1 - (minDistance / maxDistance) : 1;
            
            const clusterInfo = this.clusterCharacteristics[closestCluster];
            
            return {
                clusterId: closestCluster,
                clusterLabel: clusterInfo?.label || `Cluster ${closestCluster}`,
                confidence,
                distance: minDistance,
                clusterSize: clusterInfo?.size || 0,
                characteristics: clusterInfo?.characteristics || {}
            };
        } catch (error) {
            console.error('Error assigning cluster:', error);
            throw error;
        }
    }

    /**
     * Get cluster distribution for a group of students
     */
    getClusterDistribution(clusterAssignments) {
        const distribution = {};
        
        // Initialize distribution
        for (let i = 0; i < this.numClusters; i++) {
            const label = this.clusterCharacteristics[i]?.label || `Cluster ${i}`;
            distribution[label] = 0;
        }
        
        // Count assignments
        clusterAssignments.forEach(assignment => {
            const label = assignment.clusterLabel;
            if (distribution.hasOwnProperty(label)) {
                distribution[label]++;
            }
        });
        
        return distribution;
    }

    /**
     * Get cluster insights and recommendations
     */
    getClusterInsights() {
        if (!this.clusterCharacteristics) {
            return [];
        }
        
        return this.clusterCharacteristics.map((cluster, index) => ({
            clusterId: index,
            label: cluster.label,
            size: cluster.size,
            insights: this.generateClusterInsights(cluster),
            recommendations: this.generateClusterRecommendations(cluster)
        }));
    }

    /**
     * Generate insights for a cluster
     */
    generateClusterInsights(cluster) {
        const insights = [];
        const chars = cluster.characteristics;
        
        if (chars.attendanceRate) {
            const rate = chars.attendanceRate.mean;
            if (rate > 0.9) {
                insights.push('Excellent attendance performance');
            } else if (rate > 0.8) {
                insights.push('Good attendance performance');
            } else if (rate > 0.7) {
                insights.push('Moderate attendance concerns');
            } else {
                insights.push('Significant attendance issues');
            }
        }
        
        if (chars.consistency) {
            const consistency = chars.consistency.mean;
            if (consistency > 0.8) {
                insights.push('Highly consistent attendance patterns');
            } else if (consistency < 0.5) {
                insights.push('Irregular attendance patterns');
            }
        }
        
        if (chars.recentTrend) {
            const trend = chars.recentTrend.mean;
            if (trend > 0.1) {
                insights.push('Improving attendance trend');
            } else if (trend < -0.1) {
                insights.push('Declining attendance trend');
            }
        }
        
        return insights;
    }

    /**
     * Generate recommendations for a cluster
     */
    generateClusterRecommendations(cluster) {
        const recommendations = [];
        
        switch (cluster.label) {
            case 'Excellent Attenders':
                recommendations.push('Continue positive reinforcement');
                recommendations.push('Use as peer mentors');
                recommendations.push('Maintain current support systems');
                break;
                
            case 'Consistent Attenders':
                recommendations.push('Recognize consistent behavior');
                recommendations.push('Monitor for any changes');
                recommendations.push('Provide occasional encouragement');
                break;
                
            case 'Irregular Attenders':
                recommendations.push('Identify patterns in absences');
                recommendations.push('Implement structured check-ins');
                recommendations.push('Address underlying barriers');
                break;
                
            case 'Declining Attenders':
                recommendations.push('Immediate intervention required');
                recommendations.push('Investigate causes of decline');
                recommendations.push('Develop targeted support plan');
                break;
                
            case 'At-Risk Students':
                recommendations.push('Intensive monitoring and support');
                recommendations.push('Family engagement strategies');
                recommendations.push('Consider alternative interventions');
                break;
        }
        
        return recommendations;
    }
}

module.exports = StudentClusterer;
