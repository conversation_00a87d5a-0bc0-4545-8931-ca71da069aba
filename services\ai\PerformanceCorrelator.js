const ss = require('simple-statistics');
const { format, parseISO, differenceInDays } = require('date-fns');

/**
 * Performance Correlator for Attendance-Academic Performance Analysis
 * Analyzes relationships between attendance patterns and academic outcomes
 */
class PerformanceCorrelator {
    constructor() {
        this.correlationThresholds = {
            strong: 0.7,
            moderate: 0.5,
            weak: 0.3
        };
    }

    /**
     * Analyze correlation between attendance and academic performance
     */
    async analyzeAttendancePerformanceCorrelation(attendanceData, performanceData) {
        try {
            console.log('Analyzing attendance-performance correlation...');
            
            // Merge attendance and performance data by student
            const mergedData = this.mergeAttendancePerformanceData(attendanceData, performanceData);
            
            if (mergedData.length === 0) {
                return {
                    correlation: null,
                    message: 'Insufficient data for correlation analysis'
                };
            }

            // Calculate various correlation metrics
            const correlations = this.calculateCorrelations(mergedData);
            
            // Analyze patterns by subject
            const subjectAnalysis = this.analyzeBySubject(mergedData);
            
            // Analyze patterns by grade level
            const gradeLevelAnalysis = this.analyzeByGradeLevel(mergedData);
            
            // Generate insights and recommendations
            const insights = this.generateCorrelationInsights(correlations, subjectAnalysis, gradeLevelAnalysis);
            
            return {
                correlations,
                subjectAnalysis,
                gradeLevelAnalysis,
                insights,
                sampleSize: mergedData.length
            };
        } catch (error) {
            console.error('Error analyzing attendance-performance correlation:', error);
            throw error;
        }
    }

    /**
     * Merge attendance and performance data by student
     */
    mergeAttendancePerformanceData(attendanceData, performanceData) {
        const studentMap = new Map();
        
        // Group attendance data by student
        attendanceData.forEach(record => {
            const studentId = record.student_id;
            if (!studentMap.has(studentId)) {
                studentMap.set(studentId, {
                    studentId,
                    studentName: `${record.first_name} ${record.last_name}`,
                    gradeLevel: record.grade_level,
                    section: record.section,
                    attendance: [],
                    performance: []
                });
            }
            studentMap.get(studentId).attendance.push(record);
        });
        
        // Add performance data
        performanceData.forEach(record => {
            const studentId = record.student_id;
            if (studentMap.has(studentId)) {
                studentMap.get(studentId).performance.push(record);
            }
        });
        
        // Filter students with both attendance and performance data
        return Array.from(studentMap.values()).filter(student => 
            student.attendance.length > 0 && student.performance.length > 0
        );
    }

    /**
     * Calculate various correlation metrics
     */
    calculateCorrelations(mergedData) {
        const correlationData = mergedData.map(student => {
            const attendanceMetrics = this.calculateStudentAttendanceMetrics(student.attendance);
            const performanceMetrics = this.calculateStudentPerformanceMetrics(student.performance);
            
            return {
                studentId: student.studentId,
                attendanceRate: attendanceMetrics.attendanceRate,
                averageGrade: performanceMetrics.averageGrade,
                gradeLevel: student.gradeLevel,
                ...attendanceMetrics,
                ...performanceMetrics
            };
        });
        
        // Calculate correlations
        const attendanceRates = correlationData.map(d => d.attendanceRate);
        const averageGrades = correlationData.map(d => d.averageGrade);
        
        const primaryCorrelation = this.calculateCorrelation(attendanceRates, averageGrades);
        
        // Additional correlations
        const correlations = {
            primary: {
                coefficient: primaryCorrelation,
                strength: this.interpretCorrelationStrength(primaryCorrelation),
                description: 'Attendance Rate vs Average Grade'
            },
            detailed: this.calculateDetailedCorrelations(correlationData)
        };
        
        return correlations;
    }

    /**
     * Calculate detailed correlations between various metrics
     */
    calculateDetailedCorrelations(correlationData) {
        const metrics = [
            'attendanceRate', 'lateRate', 'absentRate', 'consistency',
            'averageGrade', 'gradeVariability', 'improvementTrend'
        ];
        
        const correlations = {};
        
        for (let i = 0; i < metrics.length; i++) {
            for (let j = i + 1; j < metrics.length; j++) {
                const metric1 = metrics[i];
                const metric2 = metrics[j];
                
                const values1 = correlationData.map(d => d[metric1]).filter(v => v !== undefined && !isNaN(v));
                const values2 = correlationData.map(d => d[metric2]).filter(v => v !== undefined && !isNaN(v));
                
                if (values1.length > 5 && values2.length > 5 && values1.length === values2.length) {
                    const correlation = this.calculateCorrelation(values1, values2);
                    
                    correlations[`${metric1}_vs_${metric2}`] = {
                        coefficient: correlation,
                        strength: this.interpretCorrelationStrength(correlation),
                        sampleSize: values1.length
                    };
                }
            }
        }
        
        return correlations;
    }

    /**
     * Calculate correlation coefficient
     */
    calculateCorrelation(x, y) {
        if (x.length !== y.length || x.length < 2) {
            return 0;
        }
        
        try {
            return ss.sampleCorrelation(x, y);
        } catch (error) {
            console.warn('Error calculating correlation:', error);
            return 0;
        }
    }

    /**
     * Interpret correlation strength
     */
    interpretCorrelationStrength(coefficient) {
        const abs = Math.abs(coefficient);
        
        if (abs >= this.correlationThresholds.strong) {
            return 'strong';
        } else if (abs >= this.correlationThresholds.moderate) {
            return 'moderate';
        } else if (abs >= this.correlationThresholds.weak) {
            return 'weak';
        } else {
            return 'negligible';
        }
    }

    /**
     * Calculate attendance metrics for a student
     */
    calculateStudentAttendanceMetrics(attendanceRecords) {
        const total = attendanceRecords.length;
        if (total === 0) {
            return {
                attendanceRate: 0,
                lateRate: 0,
                absentRate: 0,
                consistency: 0
            };
        }
        
        const present = attendanceRecords.filter(r => r.status === 'present').length;
        const late = attendanceRecords.filter(r => r.status === 'late').length;
        const absent = attendanceRecords.filter(r => r.status === 'absent').length;
        
        const attendanceValues = attendanceRecords.map(r => {
            switch (r.status) {
                case 'present': return 1;
                case 'late': return 0.5;
                case 'absent': return 0;
                default: return 0;
            }
        });
        
        return {
            attendanceRate: present / total,
            lateRate: late / total,
            absentRate: absent / total,
            consistency: attendanceValues.length > 1 ? 1 - ss.standardDeviation(attendanceValues) : 1
        };
    }

    /**
     * Calculate performance metrics for a student
     */
    calculateStudentPerformanceMetrics(performanceRecords) {
        // Note: This assumes performance data structure - adjust based on actual schema
        const grades = performanceRecords
            .map(r => parseFloat(r.grade || r.score || 0))
            .filter(g => !isNaN(g) && g > 0);
        
        if (grades.length === 0) {
            return {
                averageGrade: 0,
                gradeVariability: 0,
                improvementTrend: 0
            };
        }
        
        const averageGrade = ss.mean(grades);
        const gradeVariability = grades.length > 1 ? ss.standardDeviation(grades) : 0;
        
        // Calculate improvement trend (simple linear regression)
        let improvementTrend = 0;
        if (grades.length > 2) {
            const indices = Array.from({ length: grades.length }, (_, i) => i);
            try {
                const regression = ss.linearRegression(indices.map((x, i) => [x, grades[i]]));
                improvementTrend = regression.m;
            } catch (error) {
                improvementTrend = 0;
            }
        }
        
        return {
            averageGrade,
            gradeVariability,
            improvementTrend
        };
    }

    /**
     * Analyze correlations by subject
     */
    analyzeBySubject(mergedData) {
        const subjectMap = new Map();
        
        mergedData.forEach(student => {
            student.attendance.forEach(record => {
                const subject = record.subject_name || 'Unknown';
                if (!subjectMap.has(subject)) {
                    subjectMap.set(subject, []);
                }
                
                const attendanceMetrics = this.calculateStudentAttendanceMetrics([record]);
                const performanceMetrics = this.calculateStudentPerformanceMetrics(
                    student.performance.filter(p => p.subject_name === subject)
                );
                
                if (performanceMetrics.averageGrade > 0) {
                    subjectMap.get(subject).push({
                        studentId: student.studentId,
                        attendanceRate: attendanceMetrics.attendanceRate,
                        averageGrade: performanceMetrics.averageGrade
                    });
                }
            });
        });
        
        const subjectAnalysis = {};
        
        subjectMap.forEach((data, subject) => {
            if (data.length > 5) { // Minimum sample size
                const attendanceRates = data.map(d => d.attendanceRate);
                const averageGrades = data.map(d => d.averageGrade);
                
                const correlation = this.calculateCorrelation(attendanceRates, averageGrades);
                
                subjectAnalysis[subject] = {
                    correlation: correlation,
                    strength: this.interpretCorrelationStrength(correlation),
                    sampleSize: data.length,
                    averageAttendance: ss.mean(attendanceRates),
                    averageGrade: ss.mean(averageGrades)
                };
            }
        });
        
        return subjectAnalysis;
    }

    /**
     * Analyze correlations by grade level
     */
    analyzeByGradeLevel(mergedData) {
        const gradeLevelMap = new Map();
        
        mergedData.forEach(student => {
            const gradeLevel = student.gradeLevel;
            if (!gradeLevelMap.has(gradeLevel)) {
                gradeLevelMap.set(gradeLevel, []);
            }
            
            const attendanceMetrics = this.calculateStudentAttendanceMetrics(student.attendance);
            const performanceMetrics = this.calculateStudentPerformanceMetrics(student.performance);
            
            if (performanceMetrics.averageGrade > 0) {
                gradeLevelMap.get(gradeLevel).push({
                    studentId: student.studentId,
                    attendanceRate: attendanceMetrics.attendanceRate,
                    averageGrade: performanceMetrics.averageGrade
                });
            }
        });
        
        const gradeLevelAnalysis = {};
        
        gradeLevelMap.forEach((data, gradeLevel) => {
            if (data.length > 5) { // Minimum sample size
                const attendanceRates = data.map(d => d.attendanceRate);
                const averageGrades = data.map(d => d.averageGrade);
                
                const correlation = this.calculateCorrelation(attendanceRates, averageGrades);
                
                gradeLevelAnalysis[gradeLevel] = {
                    correlation: correlation,
                    strength: this.interpretCorrelationStrength(correlation),
                    sampleSize: data.length,
                    averageAttendance: ss.mean(attendanceRates),
                    averageGrade: ss.mean(averageGrades)
                };
            }
        });
        
        return gradeLevelAnalysis;
    }

    /**
     * Generate insights and recommendations from correlation analysis
     */
    generateCorrelationInsights(correlations, subjectAnalysis, gradeLevelAnalysis) {
        const insights = {
            overall: [],
            subjects: [],
            gradeLevels: [],
            recommendations: []
        };
        
        // Overall insights
        const primaryCorr = correlations.primary;
        if (primaryCorr.strength === 'strong') {
            insights.overall.push(`Strong ${primaryCorr.coefficient > 0 ? 'positive' : 'negative'} correlation between attendance and academic performance`);
        } else if (primaryCorr.strength === 'moderate') {
            insights.overall.push(`Moderate correlation suggests attendance impacts academic performance`);
        } else {
            insights.overall.push(`Weak correlation suggests other factors may be more influential than attendance`);
        }
        
        // Subject-specific insights
        Object.entries(subjectAnalysis).forEach(([subject, analysis]) => {
            if (analysis.strength === 'strong') {
                insights.subjects.push(`${subject}: Strong attendance-performance relationship (r=${analysis.correlation.toFixed(3)})`);
            }
        });
        
        // Grade level insights
        Object.entries(gradeLevelAnalysis).forEach(([grade, analysis]) => {
            if (analysis.strength === 'strong') {
                insights.gradeLevels.push(`Grade ${grade}: Strong attendance-performance relationship (r=${analysis.correlation.toFixed(3)})`);
            }
        });
        
        // Recommendations
        if (primaryCorr.coefficient > 0.5) {
            insights.recommendations.push('Prioritize attendance improvement initiatives');
            insights.recommendations.push('Implement early warning systems for attendance issues');
            insights.recommendations.push('Provide targeted support for students with poor attendance');
        }
        
        if (Object.values(subjectAnalysis).some(s => s.strength === 'strong')) {
            insights.recommendations.push('Focus on subject-specific attendance interventions');
        }
        
        return insights;
    }

    /**
     * Predict academic performance based on attendance patterns
     */
    predictPerformanceFromAttendance(attendanceRate, correlationData) {
        if (!correlationData || correlationData.length === 0) {
            return null;
        }
        
        // Simple linear prediction based on correlation
        const attendanceRates = correlationData.map(d => d.attendanceRate);
        const grades = correlationData.map(d => d.averageGrade);
        
        try {
            const regression = ss.linearRegression(attendanceRates.map((x, i) => [x, grades[i]]));
            const predictedGrade = regression.m * attendanceRate + regression.b;
            
            // Calculate prediction confidence based on R-squared
            const rSquared = ss.rSquared(
                attendanceRates.map((x, i) => [x, grades[i]]),
                ss.linearRegressionLine(regression)
            );
            
            return {
                predictedGrade: Math.max(0, Math.min(100, predictedGrade)),
                confidence: rSquared,
                model: {
                    slope: regression.m,
                    intercept: regression.b,
                    rSquared
                }
            };
        } catch (error) {
            console.warn('Error predicting performance:', error);
            return null;
        }
    }

    /**
     * Calculate attendance impact on grade improvement
     */
    calculateAttendanceImpact(currentAttendance, targetAttendance, correlationData) {
        const currentPrediction = this.predictPerformanceFromAttendance(currentAttendance, correlationData);
        const targetPrediction = this.predictPerformanceFromAttendance(targetAttendance, correlationData);
        
        if (!currentPrediction || !targetPrediction) {
            return null;
        }
        
        const gradeImprovement = targetPrediction.predictedGrade - currentPrediction.predictedGrade;
        
        return {
            currentGrade: currentPrediction.predictedGrade,
            targetGrade: targetPrediction.predictedGrade,
            gradeImprovement,
            attendanceImprovement: targetAttendance - currentAttendance,
            confidence: Math.min(currentPrediction.confidence, targetPrediction.confidence)
        };
    }
}

module.exports = PerformanceCorrelator;
